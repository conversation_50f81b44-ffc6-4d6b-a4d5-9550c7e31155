<!DOCTYPE html>
<!-- saved from url=(0385)https://spain.blscn.cn/CHN/Appointment/VisaType?data=wF5igIOP%2FjrdM5FY%2FQrQFFQWVnwTogcm4dDCaDVxBiZpNkYqx1aOPQM5LznaYEceIPxg3dt7PPka2UC9BYFcY92IQfy%2B*********************************%2BUOSytKye2QzOH5J3iX3RweiKC1qDZwrSJK3NoUx%2FukhstqTdEgeQrgIobfmjAEwat5sID7tamfN1MUux1dEM0VmWYSvAgAZDqIxpgLh0p%2Bu92cvCbeSBdGHhUXYT5f3spl1BbTcYcwIlMpQWU7fqCiBTqkMtpHiUBe4X0kFsHZchVHRsRitURwNLBreyLjHjDZ -->
<html lang="en" data-bs-theme="dark" class="k-webkit k-webkit137"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>China BLS Spain Visa: Welcome to the Official Website Spain Visa Application Centre in China</title>

    <!-- Meta Tags -->
    
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="China BLS Spain Visa">

    <script>

        const storedTheme = localStorage.getItem('theme')

        const getPreferredTheme = () => {
            if (storedTheme) {
                return storedTheme
            }
            return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
        }

        const setTheme = function (theme) {
            if (theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                document.documentElement.setAttribute('data-bs-theme', 'dark')
            } else {
                document.documentElement.setAttribute('data-bs-theme', theme)
            }
        }

        setTheme(getPreferredTheme())

        window.addEventListener('DOMContentLoaded', () => {
            var el = document.querySelector('.theme-icon-active');
            if (el != 'undefined' && el != null) {
                const showActiveTheme = theme => {
                    const activeThemeIcon = document.querySelector('.theme-icon-active use')
                    const btnToActive = document.querySelector(`[data-bs-theme-value="${theme}"]`)
                    const svgOfActiveBtn = btnToActive.querySelector('.mode-switch use').getAttribute('href')

                    document.querySelectorAll('[data-bs-theme-value]').forEach(element => {
                        element.classList.remove('active')
                    })

                    btnToActive.classList.add('active')
                    activeThemeIcon.setAttribute('href', svgOfActiveBtn)
                }

                window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
                    if (storedTheme !== 'light' || storedTheme !== 'dark') {
                        setTheme(getPreferredTheme())
                    }
                })

                showActiveTheme(getPreferredTheme())

                document.querySelectorAll('[data-bs-theme-value]')
                    .forEach(toggle => {
                        toggle.addEventListener('click', () => {
                            const theme = toggle.getAttribute('data-bs-theme-value')
                            localStorage.setItem('theme', theme)
                            setTheme(theme)
                            showActiveTheme(theme)
                        })
                    })

            }
        })

    </script>

    <!-- Favicon -->
    <link rel="shortcut icon" href="https://spain.blscn.cn/assets/images/favicon.png">

    <!-- Google Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
    <link rel="stylesheet" href="./上海-选类型-一组_files/css2">

    <!-- Plugins CSS -->
    <link rel="stylesheet" type="text/css" href="./上海-选类型-一组_files/all.min.css">
    <link rel="stylesheet" type="text/css" href="./上海-选类型-一组_files/bootstrap-icons.css">
    <link rel="stylesheet" type="text/css" href="./上海-选类型-一组_files/tiny-slider.css">
    <link rel="stylesheet" type="text/css" href="./上海-选类型-一组_files/glightbox.css">
    <link rel="stylesheet" type="text/css" href="./上海-选类型-一组_files/flatpickr.min.css">
    <link rel="stylesheet" type="text/css" href="./上海-选类型-一组_files/choices.min.css">

    <!-- Theme CSS -->
    <link rel="stylesheet" type="text/css" href="./上海-选类型-一组_files/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="./上海-选类型-一组_files/style.css">

    <link rel="stylesheet" type="text/css" href="./上海-选类型-一组_files/color.css">

    <link rel="stylesheet" type="text/css" href="./上海-选类型-一组_files/site.css">

    
    <link rel="stylesheet" type="text/css" href="./上海-选类型-一组_files/kendo.common.min.css">
    <link rel="stylesheet" type="text/css" href="./上海-选类型-一组_files/kendo.silver.min.css">

    <style>
        .nav-link {
            padding-right: 4px !important;
            font-size: 15px !important;
        }
    </style>
    <script src="./上海-选类型-一组_files/jquery.min.js"></script>
    <script src="./上海-选类型-一组_files/jquery.validate.min.js"></script>
    <script src="./上海-选类型-一组_files/jquery-ajax-unobtrusive.js"></script>
    <script src="./上海-选类型-一组_files/jquery.validate.unobtrusive.min.js"></script>
    <script>
        $.ajaxSetup({
            beforeSend: function (xhr, options) {
                if (options.type.toUpperCase() == "POST") {
                    xhr.setRequestHeader("RequestVerificationToken", $('input:hidden[name="__RequestVerificationToken"]').val());
                }

            }
        });
    </script>
<style type="text/css">/* Copyright 2014-present Evernote Corporation. All rights reserved. */
@keyframes caretBlink {
    from { opacity: 1.0; }
    to { opacity: 0.0; }
}

@keyframes rotateSpinner {
    from {
        transform:rotate(0deg);
    }
    to {
        transform:rotate(360deg);
    }
}

#text-tool-caret {
    animation-name: caretBlink;  
    animation-iteration-count: infinite;  
    animation-timing-function: cubic-bezier(1.0,0,0,1.0);
    animation-duration: 1s; 
}

#en-markup-loading-spinner {
    position: absolute;
    top: calc(50% - 16px);
    left: calc(50% - 16px);
    width: 32px;
    height: 32px;
}

#en-markup-loading-spinner img {
    position: relative;
    top: 0px;
    left: 0px;
    animation-name: rotateSpinner;
    animation-duration: 0.6s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}
</style><style type="text/css">/* Copyright 2014-present Evernote Corporation. All rights reserved. */
.skitchToastBoxContainer {
    position: absolute;
    width: 100%;
    text-align: center;
    top: 30px;
    -webkit-user-select: none;
    -moz-user-select: none;
    pointer-events: none;
}

.skitchToastBox {
    width: 200px;
    height: 16px;
    padding: 12px;
    background-color: rgba(47, 55, 61, 0.95);
    border-radius: 4px;
    color: white;
    cursor: default;
    font-size: 10pt;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.32);
    font-family: 'Soleil', Helvetica, Arial, sans-serif;
    border: 2px rgba(255, 255, 255, 0.38) solid;
}

.lang-zh-cn .skitchToastBox {
    font-family: '微软雅黑', 'Microsoft YaHei', SimSun,
        '&#x30E1;&#x30A4;&#x30EA;&#x30AA;', Meiryo, 'MS PGothic', 'Soleil',
        Helvetica, Arial, sans-serif;
}

.lang-ja-jp .skitchToastBox {
    font-family: '&#x30E1;&#x30A4;&#x30EA;&#x30AA;', Meiryo, 'MS PGothic',
        '微软雅黑', 'Microsoft YaHei', SimSun, 'Soleil', Helvetica, Arial,
        sans-serif;
}

.skitchToast {
    padding-left: 20px;
    padding-right: 20px;
    display: inline-block;
    height: 10px;
    color: #f1f5f8;
    text-align: center;
}

.skitchVisible {
    /* Don't remove this class it's a hack used by the Evernote Clipper */
}
</style><style type="text/css">/* Copyright 2014-present Evernote Corporation. All rights reserved. */

@font-face {
	font-family: 'Soleil';
	src: url(data:application/font-woff2;base64,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);
	font-weight: normal;
	font-style: normal;
}
</style><style type="text/css">/* Copyright 2019-present Evernote Corporation. All rights reserved. */

#en-markup-disabled {
    position: fixed;
    z-index: 9999;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    cursor: default;
    -webkit-user-select: none;
}

#en-markup-alert-container {
    position: absolute;
    z-index: 9999;
    width: 450px;
    left: calc(50% - 225px);
    top: calc(50% - 85px);
    background-color: white;
    box-shadow: 0 2px 7px 1px rgba(0,0,0,0.35);
    -webkit-user-select: none;
}

#en-markup-alert-container .cell-1 {
    position: relative;
    height: 110px;
    width: 105px;
    float: left;
    text-align: center;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAABT3SURBVFhHvZj3V5PptsfvvzBzxhHP8TrjmjnjXHXUQXoJPYAESOg1CSSBhBZCSWgJaQTpRbp0kN6ki1KkgwiE3nsRLChVKbPuDkH0ztx1zy93nb02L0/evHn2J9+9n5b/OPlXdvz/Z0dHR2et4+Oz3v9i/wLoCDo5FvVyePQJGp8OP4kaR/APGl+uHw8/iq/nN8V3wOFh+Dh8Fj4IDndEL48OofP/FesMaO/jjm8BXsH38h2vbyQZ39z1uSDle0Ha73vZAAlwOeYlhUDwfyhxLitzriB4V1R4V9T4P6gGXQVXP73+ydX4V1V4VxHcK8rcK/BBBdY/5FmXoB85poR0gIS0//fQ/12fv0E4Jb/LEBoAxCQiICCl59rCewpsCfX7l5HhP9yL+ckg4ZpR8g2ztNvWmdLYXDm7PHmHQlVyibpLGZJaoU2rvOdVjaLXGDBqDX3q0D51GLgyakVOrzH0rjbwqkLRKvXcynXheXKxOqlA1T5PCZcjB71ZpEmaJN/GJN7Qf3BNO+InFf5lSfrfAEAsmAjo06dP0j4SilwJ7agfjZKuWWTcxuXJOJYouVZoeFTrMOpRAc8wnBYzQZt1WBc+uocQ2+fwoJ/woN8ubsA+YZCYKCQmD5FTh6hpQo/UQc+UQY/kfvfEfmp8n0tMLyWiixjWYSdos+G2WAQ8M4bePGp03B5rUcrUSIXK9rnypsm3VYOvyPj9HTDOgPb29u54f6MWetk45b9sc+8CCrVKk16PYjYa8dssQ7rx0X0OiYMuqaO0jHH3nEmP9HGHqH40p1Hdv0rd/7FGYK1mcDMq4aVt/rRn5XxQ1Xxo5VxI+WxwyXRQ0RQnb5KVO+6XNuKdLHSP66dE9BKCO2zYLaa+Tw29anVdKzRJBYqoB79AkQDGGdDu7q6U3zfIyB8sMm87lCq612j5PEVxnpuFdGKj+4hJQteMMa/8af/cGWrGJCFrnMJt0DBj3NXASKvpyqrqymoayuuYyWPIstQkjaIx37aNpPaNtNZXD5vXkp+txj9Zjq1ZjKiYFxTNsB9N+qWOeMQNOEX02Ae1WTGbjOj1ei7l6mZpt+TZEjs722dAOzs7Un5/uxf7Ey5filqlwWhAQYJCunCxL8kpw9ScSZ/iOXbxIj1+yDgw3ziq2cLQUUrqtry8rJKSAgKhrKqKUFdDaKohtFTVVLwfYJrXIps3Q1s2Ipo3Ilo2oR3VtBnTuBFTvxpRuRhcMBOYOUZPHHSN6iVADbAajTyqtXGPZBD8f7z78O4LkLT/BVT8PwmF8l51uqBnSKdtbB/54QgNvlPZAr982T9l3IoYLmftoJ/QaqekKquogFBRUVFTU1NXV9fS0tLW1tbR1dFB6hlaaFEzZFyzfqfmSNIKbnuXSTLrZUPatRKFJjkzxPIVxuNldvEsO2ucAUyRPQR+q6XPE5RDsZJm6NXNrY2vgAIuGCZeg+rxazAIareKekFIGaLmTvlVLPArVpkZE1jnWISMtAyFhs3qcVFWV1BBqJ6j6Orq6unp6evrG5yaPkYHXM8IqWeiqWeqjrJEGBCkMe53sHxJer5qmpBQvhBQPBOYMUqPe+kU0okLbDJxLVe/F/XPtTfLMNDOgGRYF9BJ1yhlCMhraBc+fsA5e5xRNs+rWPHPmsS7xWooKsmrKqvTWS6F/Qx1JCRJHYlEilEAwtDQEIPBGInMxMTY1NTE3OzUTY3NTdBmGEMzNMpEXw99T1/XMRD1aJRWvsjKn2I+HKJF9TjwW608qrQN468vbMx8AZILvGCSesO5Qo3bYgrypA3TimYCSxa808exbrGaCory6hpII2NTfqh/9QhPW09NS1NEg0KhzlFMTU3Nzc0tLCwsLS2tvjJ4CffNzMxMTEwxaCM9HYPwQkrNOrt0jpc95hPf7xzSjqPXoYyTfpteG/0KiP2dafoNaqWGoM0qvt8pZ8KncM4rd5pEi9WTkZZVRWiaGJs5Ep0eJIY3TITdM9DS0daFHKHRaECBYGIOa2trW1tb7P80uGNjYwNvicn09TC+90mNb4IeL/LzJ1kPhbSILoLfE0Pz1NuTq0Ow2J0BKXAlzDN/86jRCumyTRG6FUwHlC57lkx5hBbg4h+7ptYxsus4uTWhZU0JMdleUDf6qC80X6PY2dnZ29sTPhu0wfB4vJgMHjNGm7n54JpfCWpXQkpm+Bkj9NgXjsxGY8t06YkVoQgIVNre2VbkS1jl3KbX6YZ34yFfxbPMokXnpG5LMgdpjFM3wSKt7PVtSWhrOyO9e1A3KKAxMTGBbwxJAQHEKEQi0eHUHL8yeAn3AQ4egMcszK0c3WybVgTP1iMez4XkjPnF9zlxmkxtM6XHVwZEQIeHh++33ysHSVjn/u7ToBfdS8gepRfOe2ZN4syclG9ek5S+Kycvo6ikiFBVUddU14LSEWsjpoHvDQJAPAhMJpOdnJycnZ1dPhu0weA+kJFIJFDL1gZLcMQ2LgY3b0bAnP5oPCCp35XXYoHNljkD2tvfe/f+jbJAwibvrt9TVGwfMXfcF/KVNWyHUJOVuiunqKgIUw4McvGwgjElztQ5DQgA8SgUChC4ublRqVR3d3faqUEb7ojJAAug7fD2BBK+YTbo+ZvI2sXIgkl2yqB7UKsVNld2cm1IVNQ7uzsApHRfAp8vxWwyiO93zJvwr1pnFE1SFJRk5WRkEQiEqqqqpqamjo4ODCtxsqCKIVPnNCCMq6srhAcIT09Pr88GbTDgE2MBNIlIsifgHw+xOrYi65eiYbFLFdIErda4HJnp9WGRQgD0/sM7RIiEfZFUYDMmYYCSPxlQt+FXv8LQ0FGSkxYB/VUeKE8oCMjUOQ1Ehdje3t4MBsPns0GbTqcDmYeHB+ACE9mRYk+wL+727/4Q1bASWzLNSx/yDG63xuXKzL6aOFNo6/1bldBLhGIZdgsmcZBSMBVQv87s2OIbW2vJSMmK8wUjC+bAc3kgWVCkUBbiTIlpIDZA+Pn5+fv7BwQEMMECmNCEmwAqZnJ2cibYE/Na/Xq3o56uxJbO8NKHPe932OAfiYBECu3u7YJCqmESxFIZznNMspBSOMWsW2UPHIWTvY2lfpf7Ol8wB4qr51weKA5IBwSDkBBYzBEYGMjhsH19/ClOoAjJxdnVh+EDOkFCXV1cCfaknEa/FzuRT1celM5yASik08YuX3b21fhXQBGXSKUy3FYjMVDNCnv4OIob5wBAsILCmvWnfImrB+pUnCwIBjQgDIvFYrPZXC43kMXmhfp0TxRMrNamFgYDCp3OABWpblQiwTHriX/ffvjT1ZiyWV7miGfoKdCcOGUwykRAUZdIZbLcdqOkIUrBtH/NMg8Uyqj3h5T9CQhGu7icxfkCIJAHkuXr6wvygDZAw+cHsdjMrunst0e9bw/7dk5ehkQzvTxFxQT0JAI5q57Ztx/2dDW6bJabMeoR2mVjVyAz92pcBLR/sL/1YUst+hKpXI7XjkkaIhdM+9UsC3oPghvGw5SUlCBl4oqGZesc6LyAxPkCICgdkIfD4fD5fIFAcD+Un5kflZAWmpgpqG1JTckK9fZifAai5NQHvjwIfboaVTbHyRylhXZZ2xfKzG9MilImBlKPuehQIcPtACDH/GmfmqXg9ve8/vcJaBNdJSWVr4H+VNHnQP5+onyJgYKDg0NCQgCMy+EFsjhQV4FsJuT0a6AX8IVXIktn2Rkj7iFdVvgCqcXNaZFCBwcHAKQRe8mxUpZ3CpQ3Ta+GmfQNZ+r4IcXTUkFO6f9I2XlF0+neUM5QQDweLygoCEQCAzjIIIBCeX0Bsqfk1LH7DoKfrISVzLIyRqn3uy3tCqWWXp9uPz5+/AhLh9aDS+RKGW4nOnGI+GjK8/Ei79kGd+I4ISKdJiet8KeiPp8Sz2qI5uHpTaMySCIlROOLA0yAAgYNeAmgkFAxEIx8oh05u4794iCofvl+8Yx/2ohLcJcZvlBy+fWsCAgOH9vbHzTjQSEpbqd+gtA+Z4JaNs96ss4THoVVvghRVFASD3vYb/x12IsmISrNw5vq4m8BE5C4rkEn4ACDxrk84qkIFLXHO+TUM3sOuDVL/IJpxsNhiqDTBFv0+xcgUEgz4ZJD5V12h16cEJc57loy51u/EtSxzR7bzzC10ocyOl83YCMG+w1xGcHaJJqmXdy8GbToQtH0CDPh2ZR4atAW00CRidcQ+AJ2eGJ+C7Nrn125yM6b9koZdgzqMsIX/76wOXWWsg8f3iMTLzpUSQZ26MYO2qSPUwpmvKuXeY2brOnj5PwnwQpyigryCKSWNmxW/7qQUSjOdD/q5PtSVoirHY7kTvVg0H1P1YJp2h+mg3MayC9kmUggPn7Jbt0OqFhg5k65Jw0TeV0YfMnvcxunE+PBxwNQCIBIVXeYHdpRA+YpY6RH07THi8yGdX7nB/bicX7zcBonlGZhbQTDTVMDqauDMsKY2ljb2tvZw+bHDkcMiw84/mN4cbc2uzKYHeJJ83YiO4k2GySiI83dQ0wDyYJpHZ53cCQ1LwQ/e8conffNmnSJH8LzuvRxJbdmN8ZEQGdFnXyRWHWL2a4ROWCaPGIPz5XM02tXOE0bgp5t7uxx4qvjktn9svbJrOyK0OBYupsn0crWzNTMxAhjYm5p2j6U88cfY4d/DH086Xl/0rR4WDn6urh7Jje/Npzq5QhLBxgkF2Z2WMjIzoTON4K6TY/CWa/0cdIDoQ2nUw9XBkCjZwptbW9ppwDQb/5taqH96Phh24wJcsGcx+OlgIZ1Qevr8J6tMOFu6NRR9NJx8uZx/puT8rWTx9M7ZV2z2bXdCTAj7570bJ90vngdkTNsEd+lF9d9r3CcuHZY+emks30qheRIIJMp4q2jrTXWO8Cpa5tbueaeN0NNGSdEC81ZHdq40t8A6HQeOgVCpl60r77h06Z8v08/dsgyZYyQPe1SvOBdvcJ69iq49XV071bS4Hbq2G7a1P7D+U8Plw7TV48zNo/z3h0XvT8p2zpprJn3IKf/bOOrgHXXxVJRFCZmaC3j3UnD0lGRf5AzHgeDkgBlZ2ZiJYih9R7CUdg5e4qSOIqNHDBhdWjhym/MbJzuh85qKO2iXdWvjDYFfp9O5KBxwohNxiQpb45atuRTu8p+9gp0iuzeShj4kDa8nT2xmz+9XzR3ULL0sWzlU8XGUf7UXqJvkbw13pyIc3IgUPA2ZA9/4sRu6vpR2cpxniDe1cpCdASAAwhs8uPz6W0H3nlzDumThLhhq/B+dECHGrb8+uSrwbOlAxZXEVD1Na9WaW6PZugAiGSWPIbNnHI4ZWJUrzKfvgp6vhne8Tamdyux/0Pq0Hbm6E7O5G7B7H7R0mFy8zqDyNSwNhdv4y2sbSx9+Y7Te5nzBzlzx2lB8WRzU9giiE5CcDQrf8Gte0/JmiYkj9lEC42D+/R82xHYil/H1/thg3+mkE76RXz1zx7PpVg9asH9upFCo/hRy9QJu6wZh4J5aumid9VqQN06t3EjuOV1RMfb2J6thL73yYMfUoe306c+RpXPOpiTkRamNnB8SyryXTqomjsonfuYPbn/cOhjREAMnH5gyTGHEWBta9G0yitax6VO4uJHzcMHDYL6kJAZbMW10bXez0Af3mlnfo+r/tn9+Z2AbiV+H4iEih4yShizejiBA6a8OZeiBY+KZZ+aNWb9OufphqB5M6RVRBbd+y52cI9fNG1ngkdCOozRppENmKfvXBpfezx55V67SsmaMCIx9DEGprDs6OroezFJnQeB6XNmiWPmUUPo+/26nF51epsstvKX4dUuUcpgpoai1s36Hlt9ldpyy69Ljv1CTdCPDBOiokeM4sfMUyaxGTPE3DlK4QK1ZMkLsKpXA2rXYG3hNm4KWl4Hd237lc47GOHUDfWNMIYmxg6KVuE/4B/8SIj7hRB5w8ZXDmOERhuiDQ0MNdW0M+sC6ncpCRNG0SPoMOE9/ktNVg/Cs00KW/nzyGq3CAj+YC3TyQaFfnRtucnolGb2KHD7VIEpVKgXNYyOGzNLmrBOm8JnzZAAK3/epXDRvWTJs3yFXrXqW7vm3/DWrX7Tw9pFS0cLBQuwvh4adc8Qdc/g1A31dA1RKH1YdpBaOuZWmLbXIRnLmJgRgzChblC/JvuFin+3PK1N0rbyp8lXA6JhD39wcgUg25ofXVque3bc8e2WZvYqcPpUg/q1QoS6EcP6MaNGceOmiRNWD6ew6dP4zFlizpzDo3lKwaJL8SK1ZM2uacebn2qnIIvQ0YFtga4eHG7hIvIzQyK15WWV0spYDfu0yDGtMKGOYECD04cI6FGgd0m5Pb9pXv7Dyps5ONefne11cr6zqfpPl+cAdJvefVfE9EIhsE+Z168qGNQKFWGhokcNHoyJyJImLFMmrVOnbNNncFlz9jkLNgVrNu1vw315FFVVNQV5JTjpQnhFeQTspeRkFOVkFGB5DoljvPwU9WAWeV+oKRhQ57xUDuiVh0BenZLkpl/RxZfgePgFSDPzW6uqy84t193bb3h23PLukmT0SPn1iLDYfUrcfpWgAfXgQRAMGTZ8L3JEP2rEIHYUHTdunDBhnDxpnjyDzluz7N6LaByPy6oKis9hRaT4CmI8BTFe0EjK5zwbTeg9CEta0AsSIoIGVDh9IhqfHmlGt5Rnh6Rd/VXtnO8gUWdAu7u7mg+/Q5dcIjT87Pr8OrX9pnv7TVqHyD07b3l23vbulvTpBj7ZgF45Zp8SKMd5qcLtV+X3awQPat4XIkOHkGEjWpETmqmLmLxXuJLXpIo3zo/fuFa8cS17Qy7YsE9ZNLo/Ch9RZvcps14o+fcq+HbL0kGeDimnphsmZZcBADDOgPb29khpmlqZ36JLLlpVX7atvYKt/xFXfxX35Cr+yVW7Jz/ZN/xMePoL8dkvpGfXHJ796tgIfp3cdB36cm6+6dxy0+X5LbfWO+5tZ05rv+Pefpvadsut7aZrK7x7w7nlBjzv2PQrqfEa9AO92TX8hK+7al17xbj0klbWt/apKl9+FoaRP7c4a/FATi3pO2TOt7r534ldT+Tf3yu4gPrs+oUXP/slcINzLzpzQ/Di0+upG3x5/iKq8KwT6BC6FYfQzv0WgkLo2YWZLz+cw0Db399fXl7u7e1tbm5u/DcahIOgEBrkAYwzIDETAMJdSCTU+L/NIBwEhdBimpOTk/8GkPkddVxNk+cAAAAASUVORK5CYII=);
    background-position: 65% 50%;
    background-repeat: no-repeat;
}

#en-markup-alert-container .cell-2 {
    position: relative;
    float: left;
    width: 345px;
    margin-top: 29px;
    margin-bottom: 20px;
}

#en-markup-alert-container .cell-2 .cell-2-title {
    margin-bottom: 5px;
    padding-right: 30px;
    font-size: 12pt;
    font-family: Tahoma, Arial;
}

#en-markup-alert-container .cell-2 .cell-2-message {
    padding-right: 30px;
    font-size: 9.5pt;
    font-family: Tahoma, Arial;
}

#en-markup-alert-container .cell-3 {
    position: relative;
    width: 450px;
    height: 60px;
    float: left;
    background-color: rgb(240,240,240);
}

#en-markup-alert-container .cell-3 button {
    position: absolute;
    top: 12px;
    right: 15px;
    width: 110px;
    height: 36px;
}

#en-markup-alert-container .cell-3 button.alt-button {
    position: absolute;
    top: 12px;
    right: 140px;
    width: 110px;
    height: 36px;
}
</style></head>
<body class="" style="">
    <div class="preloader animate__animated animate__fadeOut" style="display: none;">
        <div class="preloader-item">
            <div class="spinner-grow text-primary"></div>
        </div>
    </div>
    <div id="global-overlay" class="global-overlay">
        <div class="global-overlay-loader">
        </div>
    </div>
    <header class="navbar-light header-sticky">
        <!-- Nav START -->
        <nav class="navbar navbar-expand-xl z-index-9 navbar-divider">
            <div class="container">
                <!-- Logo START -->
                <a class="navbar-brand" href="https://web.blscn.cn/">
                    <img class="light-mode-item navbar-brand-item" src="./上海-选类型-一组_files/logo.png" alt="BLS Logo" title="BLS Logo">
                    <img class="dark-mode-item navbar-brand-item" src="./上海-选类型-一组_files/logo.png" alt="logo">
                </a>
                <!-- Logo END -->
                <!-- Responsive navbar toggler -->
                <button class="navbar-toggler ms-auto" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
                    <i class="bi bi-search fs-4"> </i>
                </button>

                <!-- Responsive navbar toggler -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse2" aria-controls="navbarCollapse2" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-animation">
                        <span></span>
                        <span></span>
                        <span></span>
                    </span>
                </button>

                <!-- Main navbar START -->
                <div class="navbar-collapse collapse" id="navbarCollapse">
                    <div class="col-md-8">
                        <div class="nav my-3 my-xl-0 px-4 flex-nowrap align-items-center">
                            <div class="nav-item w-100">
                                <div class="align-items-center position-relative">
                                    <div class="align-items-center text-primary" style="font-size:30px;font-weight:700;">Apply for VISA to Spain In China</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="align-items-center position-relative">
                            <small class="text-secondary mb-1">Follow us on</small>
                            <ul class="list-inline mb-3 mt-0">
 
                                <li class="list-inline-item"> <a class="btn btn-sm shadow px-2 bg-wechat mb-0" href="https://mp.weixin.qq.com/s/UpsmZ9bmpvAPoNF8Iw6HdA" target="_blank" rel="noopener noreferrer"><i class="fab fa fa-weixin"></i></a> </li>
                                <li class="list-inline-item"> <a class="btn btn-sm shadow px-2 bg-weibo mb-0" href="https://weibo.com/u/6473596004" target="_blank" rel="noopener noreferrer"><i class="fab fa fa-weibo"></i></a> </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Main navbar END -->
                <!-- Profile and notification START -->
                <ul class="nav flex-row align-items-center list-unstyled ms-xl-auto">
                    <li class="dropdown nav-item">
                        <a class="nav-link small pb-2" href="https://spain.blscn.cn/CHN/Appointment/VisaType?data=wF5igIOP%2FjrdM5FY%2FQrQFFQWVnwTogcm4dDCaDVxBiZpNkYqx1aOPQM5LznaYEceIPxg3dt7PPka2UC9BYFcY92IQfy%2B*********************************%2BUOSytKye2QzOH5J3iX3RweiKC1qDZwrSJK3NoUx%2FukhstqTdEgeQrgIobfmjAEwat5sID7tamfN1MUux1dEM0VmWYSvAgAZDqIxpgLh0p%2Bu92cvCbeSBdGHhUXYT5f3spl1BbTcYcwIlMpQWU7fqCiBTqkMtpHiUBe4X0kFsHZchVHRsRitURwNLBreyLjHjDZ#" role="button" id="languageDropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <img class="w-30px me-2" src="./上海-选类型-一组_files/en-US.svg" alt=""><small>English</small>
                        </a>
                        <ul class="dropdown-menu dropdown-animation dropdown-menu-end min-w-auto" aria-labelledby="languageDropdown">
                            <li> <a class="dropdown-item me-4" href="javascript:OnLanguageChange(&#39;en-US&#39;);"><img class="fa-fw me-2" src="./上海-选类型-一组_files/en-US.svg" alt="">English</a> </li>
                            <li> <a class="dropdown-item me-4" href="javascript:OnLanguageChange(&#39;es-ES&#39;);"><img class="fa-fw me-2" src="./上海-选类型-一组_files/es-ES.svg" alt="">Español</a> </li>
                            <li> <a class="dropdown-item me-4" href="javascript:OnLanguageChange(&#39;zh-hans-cn&#39;);"><img class="fa-fw me-2" src="./上海-选类型-一组_files/zh-hans-cn.svg" alt=""> 中文 </a> </li>
                        </ul>
                    </li>
                        <li class="nav-item ms-3 dropdown">
                            <!-- Avatar -->
                            <a class="avatar avatar-sm p-0 mt-0" href="https://spain.blscn.cn/CHN/Appointment/VisaType?data=wF5igIOP%2FjrdM5FY%2FQrQFFQWVnwTogcm4dDCaDVxBiZpNkYqx1aOPQM5LznaYEceIPxg3dt7PPka2UC9BYFcY92IQfy%2B*********************************%2BUOSytKye2QzOH5J3iX3RweiKC1qDZwrSJK3NoUx%2FukhstqTdEgeQrgIobfmjAEwat5sID7tamfN1MUux1dEM0VmWYSvAgAZDqIxpgLh0p%2Bu92cvCbeSBdGHhUXYT5f3spl1BbTcYcwIlMpQWU7fqCiBTqkMtpHiUBe4X0kFsHZchVHRsRitURwNLBreyLjHjDZ#" id="profileDropdown" role="button" data-bs-auto-close="outside" data-bs-display="static" data-bs-toggle="dropdown" aria-expanded="false">
                                    <img onerror="OnPhotoError(this);" class="avatar-img rounded-2" src="./上海-选类型-一组_files/01.jpg" alt="Profile">
                            </a>

                            <!-- Profile dropdown START -->
                            <ul class="dropdown-menu dropdown-animation dropdown-menu-end shadow pt-3" aria-labelledby="profileDropdown">
                                <!-- Profile info -->
                                <li class="px-3 mb-3">
                                    <div class="d-flex align-items-center">
                                        <!-- Avatar -->
                                        <div class="avatar me-3">
                                                <img onerror="OnPhotoError(this);" class="avatar-img rounded-circle shadow" src="./上海-选类型-一组_files/01.jpg" alt="Profile">

                                        </div>
                                        <div>
                                            <a class="h6 mt-2 mt-sm-0" href="https://spain.blscn.cn/CHN/Appointment/VisaType?data=wF5igIOP%2FjrdM5FY%2FQrQFFQWVnwTogcm4dDCaDVxBiZpNkYqx1aOPQM5LznaYEceIPxg3dt7PPka2UC9BYFcY92IQfy%2B*********************************%2BUOSytKye2QzOH5J3iX3RweiKC1qDZwrSJK3NoUx%2FukhstqTdEgeQrgIobfmjAEwat5sID7tamfN1MUux1dEM0VmWYSvAgAZDqIxpgLh0p%2Bu92cvCbeSBdGHhUXYT5f3spl1BbTcYcwIlMpQWU7fqCiBTqkMtpHiUBe4X0kFsHZchVHRsRitURwNLBreyLjHjDZ#">MIN XU</a>
                                            <p class="small m-0"><EMAIL></p>
                                        </div>
                                    </div>
                                </li>

                                <!-- Links -->
                                <li> <hr class="dropdown-divider"></li>
                                <li></li>
                                <li><a class="dropdown-item" href="https://spain.blscn.cn/CHN/account/DeleteUser"><i class="fa fa-trash fa-fw me-2"></i>Delete Account</a></li>
                                <li><a class="dropdown-item" href="https://spain.blscn.cn/CHN/account/ChangePassword"><i class="fa fa-key fa-fw me-2"></i>Change Password</a></li>
                                <li><a class="dropdown-item bg-danger-soft-hover" href="javascript:OnLogout();"><i class="fa fa-power-off me-2"></i>Logout</a></li>
                                <li> <hr class="dropdown-divider"></li>

                                <!-- Dark mode options START -->
                                <!-- Dark mode options END-->
                            </ul>
                            <!-- Profile dropdown END -->
                        </li>

                </ul>
                <!-- Profile and notification END -->
            </div>

        </nav>
        <!-- Nav END -->
        <!--Main menu link START -->
        <nav class="navbar navbar-expand-xl navbar-divider">
            <div class="navbar-nav nav-fill w-100">
                <!-- Main navbar START -->
               <div class="navbar-collapse w-100 collapse" id="navbarCollapse2">
                   <ul class="navbar-nav nav-active-line navbar-nav-scroll" style="padding-left: 386px;">
                        <li class="nav-item"> <a class="nav-link home-active" href="https://web.blscn.cn/"><i class="fa fa-home"></i></a>	</li>

                        <!-- Nav item -->
                        <li class="nav-item"> <a class="nav-link new-app-active" href="https://spain.blscn.cn/CHN/appointment/newappointment">Book New Appointment</a>	</li>

                        <!-- Nav item -->

                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle my-app-active" href="https://spain.blscn.cn/CHN/Appointment/VisaType?data=wF5igIOP%2FjrdM5FY%2FQrQFFQWVnwTogcm4dDCaDVxBiZpNkYqx1aOPQM5LznaYEceIPxg3dt7PPka2UC9BYFcY92IQfy%2B*********************************%2BUOSytKye2QzOH5J3iX3RweiKC1qDZwrSJK3NoUx%2FukhstqTdEgeQrgIobfmjAEwat5sID7tamfN1MUux1dEM0VmWYSvAgAZDqIxpgLh0p%2Bu92cvCbeSBdGHhUXYT5f3spl1BbTcYcwIlMpQWU7fqCiBTqkMtpHiUBe4X0kFsHZchVHRsRitURwNLBreyLjHjDZ#" id="pageMenu" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Manage Appointments</a>
                            <ul class="dropdown-menu" aria-labelledby="pageMenu">

                                <li>
                                    <a class="dropdown-item" href="https://spain.blscn.cn/CHN/appointmentdata/BLSCancelAppointment">
                                        <i class="text-danger fa-regular fa-calendar-xmark me-2"></i>Cancel Appointment
                                    </a>
                                </li>

                                <li>
                                    <a class="dropdown-item" href="https://spain.blscn.cn/CHN/appointmentdata/BLSReprintAppointmentLetter">
                                        <i class="text-info fa-solid fa-print me-2"></i>Re-Print Appointment Letter
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="https://spain.blscn.cn/CHN/appointmentdata/myappointments">
                                        <i class="text-info fa-solid fa-users me-2"></i>Manage Profile
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle my-app-active" href="https://spain.blscn.cn/CHN/Appointment/VisaType?data=wF5igIOP%2FjrdM5FY%2FQrQFFQWVnwTogcm4dDCaDVxBiZpNkYqx1aOPQM5LznaYEceIPxg3dt7PPka2UC9BYFcY92IQfy%2B*********************************%2BUOSytKye2QzOH5J3iX3RweiKC1qDZwrSJK3NoUx%2FukhstqTdEgeQrgIobfmjAEwat5sID7tamfN1MUux1dEM0VmWYSvAgAZDqIxpgLh0p%2Bu92cvCbeSBdGHhUXYT5f3spl1BbTcYcwIlMpQWU7fqCiBTqkMtpHiUBe4X0kFsHZchVHRsRitURwNLBreyLjHjDZ#" id="pageMenu" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Travel Insurance</a>
    <ul class="dropdown-menu" aria-labelledby="pageMenu">
        <li class="dropdown-item"><a class="nav-link doorstep-active fw-bolder custom-link" href="http://bzx.instar.vip/#/insure-page">Ping’An</a></li>
        <li class="dropdown-item"><a class="nav-link doorstep-active fw-bolder custom-link" href="https://dlc-h5.zhongan.com/visaCenter.html?visa=BLS&amp;channelId=&amp;supplierNo=&amp;realChannelId=afblh62n955src84rw&amp;country=%E8%A5%BF%E7%8F%AD%E7%89%99_Spain">ZhongAn</a></li>
    </ul>
</li>
  <li class="nav-item"> <a class="nav-link faq-manageagent" href="https://spain.blscn.cn/CHN/Agent/login">Agent Login</a></li>

                    </ul>

                </div>
                <!-- Main navbar END -->
            </div>
        </nav>
        <!--Main menu link END -->
    </header><div id="sticky-space" style="height: 0px;"></div>
    <!-- **************** MAIN CONTENT START **************** -->
    <main>
        
<style>
    .validation-summary ul {
        list-style-type: none;
    }
</style> 
<div class="row">
    <div class="col-12 row p-4 justify-content-center">
        <div class="col-12 justify-content-center text-center pb-2">
            <h5>Book New Appointment - Visa Type Selection</h5>
        </div>
    </div>
    <div class="col-12">
        <style>
.bls-card{
    border:1px solid gray;
}
.form-check-input {
    margin-top: 0.2em !important;
    margin-left: 0px !important;
    margin-right: 5px !important;
}
.ikhhzz{z-index:1;}.wqswh{z-index:1;}.bbbwgi{z-index:1;}.fbwbwhud{z-index:1;}.fnpssl{z-index:1;}.cqlidbxn{z-index:1;}.ampgzpw{z-index:1;}.bahoglre{z-index:1;}.pkego{z-index:1;}.pgygvaq{z-index:1;}.dimwv{z-index:1;}.whqrvr{z-index:1;}.efvetddl{z-index:1;}.iilzzaa{z-index:1;}.btlwdf{z-index:1;}.owzdiunk{z-index:1;}.uladbbwmr{z-index:1;}.aekwfwgh{z-index:1;}.cgvaacpx{z-index:1;}.zdsux{z-index:1;}.enzxhzx{z-index:1;}.nfoez{display:none !important;z-index:1;}.hbnxiqzl{display:none !important;z-index:1;}.aldrn{display:none !important;z-index:1;}.xfhvrrx{display:none !important;z-index:1;}.zqfiqm{display:none !important;z-index:1;}.nlxlsbfxp{display:none !important;z-index:1;}.fcywwvfkq{display:none !important;z-index:1;}.zhiibks{display:none !important;z-index:1;}.hciiygn{display:none !important;z-index:1;}.fgvnsisxm{display:none !important;z-index:1;}.ymcuaxa{display:none !important;z-index:1;}.vgcpjiirq{display:none !important;z-index:1;}.qwesjxp{display:none !important;z-index:1;}.pcxqplwml{display:none !important;z-index:1;}.lmckia{display:none !important;z-index:1;}.hwqwmfa{display:none !important;z-index:1;}.fgpkgcfe{display:none !important;z-index:1;}.hxaeq{display:none !important;z-index:1;}.txxbgdkf{display:none !important;z-index:1;}.dhcazbd{display:none !important;z-index:1;}.wykegbedg{display:none !important;z-index:1;}.bdkafq{display:none !important;z-index:1;}.xzpdtp{display:none !important;z-index:1;}.bhyze{display:block !important;z-index:1;}.zezdg{display:block !important;z-index:1;}.mmvsxa{display:block !important;z-index:1;}.icdcbmucb{display:block !important;z-index:1;}.ucjodeu{display:block !important;z-index:1;}.ebcbyl{display:block !important;z-index:1;}.zjtkewftt{display:block !important;z-index:1;}.zyexnjc{display:block !important;z-index:1;}.qfbxrihan{display:block !important;z-index:1;}.yshdrfcc{display:block !important;z-index:1;}.rxqpj{display:block !important;z-index:1;}.cegqiqr{display:block !important;z-index:1;}.ehfvcslcd{display:block !important;z-index:1;}.lubfwg{display:block !important;z-index:1;}.ulilzzpsh{display:block !important;z-index:1;}.ebkedgs{display:block !important;z-index:1;}.vljzk{display:block !important;z-index:1;}.puhdooxi{display:block !important;z-index:1;}.rnfjoehpd{display:block !important;z-index:1;}.dgbsehni{display:block !important;z-index:1;}.auoub{display:block !important;z-index:1;}.fsmdtrwbt{display:block !important;}.yxdynvgf{display:block !important;}.jobcva{display:block !important;}.uhecte{display:block !important;}.sypdablj{display:block !important;}.znjpfdnyi{display:block !important;}.tjiepqco{display:block !important;}.pwqxuhlax{display:block !important;}.gdwzobpm{display:block !important;}.artgudk{display:block !important;}.kuznxn{display:block !important;}.ucjppit{display:block !important;}.pgclx{display:block !important;}.tcadrn{display:block !important;}.vqvuka{display:block !important;}.fyeiq{display:block !important;}.emszxc{display:block !important;}.nezsrm{display:block !important;}.rrzjfzizl{display:block !important;}.eitxrctvq{display:none !important;}.gjbgp{display:none !important;}.sqrddy{display:none !important;}.jnyud{display:none !important;}.zfbfesun{display:none !important;}.nxyvwrk{display:none !important;}.ydpsqi{display:none !important;}.krgejjsh{display:none !important;}.eqzfewov{display:none !important;}.cduehcmy{display:none !important;}.arnyjx{display:none !important;}.umlpdf{display:none !important;}.pirfsfdbp{display:none !important;}.ynezfkes{display:none !important;}.erpcghc{display:none !important;}.iccuorfl{display:block !important;}.vrsekj{display:block !important;}.sagwoxm{display:block !important;}.rrzxfhh{display:block !important;}.lmouakdz{display:block !important;}.ahhyqt{display:block !important;}.dnouycek{display:block !important;}.qdwsigyo{display:block !important;}.hswpkiwgr{display:block !important;}.qfnayju{display:block !important;}.smuihdfk{display:block !important;}.hhxuis{display:block !important;}.jbsodgxrl{display:block !important;}.ukccyjnn{display:block !important;}.bezktj{display:block !important;}.apzuuhmor{display:block !important;}.qgpmpcxsi{display:block !important;}.hfiyjgcr{display:block !important;}.jqzedghs{display:block !important;}.vnynkbsh{display:block !important;}.umccskpsv{display:block !important;}.bknfvu{display:block !important;}.grcmgh{display:block !important;}.fwvwuv{display:block !important;}.ciiesh{display:none !important;}.oolkarghh{display:none !important;}.tcvbe{display:none !important;}.ofswvps{display:none !important;}.aavifwqca{display:none !important;}.cnavgt{display:none !important;}.pntbslexv{display:none !important;}.zalcyvnk{display:none !important;}.vyblmqcny{display:none !important;}.lsyyxgcz{display:none !important;}.hprkjcrco{display:none !important;}.drnkhx{display:none !important;}.nchkq{display:none !important;}
</style>
<script>
//      $(function () {
//         $('#alertModal').modal('show');
// });

    var applicantId = 0;
    var catItem = null;
    var locationDataItem = null;
    var familyModalClose = false;
    function onDrpOpen(e) {
        var id = e.sender.element[0].id;
        var item = rspData.find(x => x.Id === id);
        rspData = RemoveItem(id);
        if (item !== null && item !== undefined) {
            item.Start = new Date($.now());
            item.Selected = false;
        }
        else {
            item = { Id: id, Start: new Date($.now()), End: null, Total: null, Selected: false };
        }
        rspData.push(item);
    }
    function onselect1(id) {
        $('.bls-applicant').removeClass('alert-primary');
        $('.bls-applicant').addClass('alert-light');
        $("#app-"+id).removeClass('alert-light');
        $("#app-"+id).addClass('alert-primary');
        $("input[name='Applicant']").prop('checked', false);
        $("#rdo-" + id).prop('checked', true);
    }
     function onDrpSelect(e) {
         var id = e.sender.element[0].id;
         var item = rspData.find(x => x.Id === id);
         rspData = RemoveItem(id);
         if (item !== null && item !== undefined) {
             item.Selected = true;
             rspData.push(item);
         }
     };
     function RemoveItem(id) {
             return rspData.filter(function (e) {
                 return e.Id !== id;
             });
     }
     function onDrpClose(e) {
         var id = e.sender.element[0].id;
         var item = rspData.find(x => x.Id === id);
         rspData = RemoveItem(id);
         if (item !== null && item !== undefined) {
             if (item.Selected) {
                 item.End = new Date($.now());
                 item.Total = item.End - item.Start;
                 item.Selected = true;
             }
             rspData.push(item);
         }
     }
    function OnSubmitVisaType() {
        ShowLoader();
        var submittedData={pjirff : $("#pjirff").val(),
cjflcz : $("#cjflcz").val(),
hglorb : $("#hglorb").val(),
ovjkxw : $("#ovjkxw").val(),
jzwjc : $("#jzwjc").val(),
tkrfk : $("#tkrfk").val(),
upiayn : $("#upiayn").val(),
jqeixm : $("#jqeixm").val(),
dsmdx : $("#dsmdx").val(),
phvheo : $("#phvheo").val(),
gpcmwplx : $("#gpcmwplx").val(),
mpnkgnn : $("#mpnkgnn").val(),
etfhj : $("#etfhj").val(),
xesba : $("#xesba").val(),
tznhkij : $("#tznhkij").val(),
sswcy : $("#sswcy").val(),
hcgflcvkb : $("#hcgflcvkb").val(),
mowfjxdv : $("#mowfjxdv").val(),
wycprkap : $("#wycprkap").val(),
pntczgh : $("#pntczgh").val(),
adxuvn : $("#anadxuvn").val(),
hgshaun : $("#anhgshaun").val(),
szjxtz : $("#anszjxtz").val(),
qeppq : $("#anqeppq").val(),
haibcg : $("#anhaibcg").val(),
nrtmw : $("#nrtmw").val(),
jdqcu : $("#jdqcu").val(),
djkksbhsf : $("#djkksbhsf").val(),
sgdmhussm : $("#sgdmhussm").val(),
mappywgy : $("#mappywgy").val(),
};;
        $("#ResponseData").val(JSON.stringify(submittedData));
        return true;
    }
    function OnAppointmentForChange(e, id) {
        applicantId = id;
        $("#members"+id).hide();
        $("#an"+id).data("kendoDropDownList").value("");
        $("#an"+id).data("kendoDropDownList").value(null);
        if (e !== null && e.target.id == "family" + id) {
        $("#members" + id).show();
        $('#AppointmentFor').val('Family');
         $('#familyDisclaimer').modal('show');
        }
        else { 
            $('#AppointmentFor').val('Individual');
        }
    }
    function OnFamilyReject(){
        var appointmentFor = document.getElementById("self" + applicantId);
        appointmentFor.checked = true;
        OnAppointmentForChange(null, applicantId);
        familyModalClose = true;
        $('#familyDisclaimer').modal('hide');
    }
    function OnFamilyAccept() {
        familyModalClose = true;
        $('#familyDisclaimer').modal('hide');
    }
    function OnPlReject(){
        $("#"+catItem).data("kendoDropDownList").value(null);
        $("#PremiumTypeModel").modal('hide');
    }
    var addressModalClose = false;
    var applicantsNoFilterData = [];
    var visaTypeFilterData = [];
    var visasubIdFilterData = [];
    var locationFilterData = [];
    var missionFilterData = [];
    var categoryFilterData = [];
    var applicantId = 0;
    var rspData = [];
    var locationData =[{"Id":"4716","Name":"Wuhan","Code":"WUHAN","VisaTypeIds":null,"VisaSubTypeIds":null,"MissionId":"d133459a-6482-45ed-bd00-5ff32aa8b71b"},{"Id":"4717","Name":"Xi'an","Code":"XIAN","VisaTypeIds":null,"VisaSubTypeIds":null,"MissionId":"d133459a-6482-45ed-bd00-5ff32aa8b71b"},{"Id":"4718","Name":"Jinan","Code":"JINAN","VisaTypeIds":null,"VisaSubTypeIds":null,"MissionId":"d133459a-6482-45ed-bd00-5ff32aa8b71b"},{"Id":"4719","Name":"Chongqing","Code":"CHONGQING","VisaTypeIds":null,"VisaSubTypeIds":null,"MissionId":"59bba02e-559e-49d3-9933-839d1b1588ec"},{"Id":"4720","Name":"Shenyang","Code":"SHENYANG","VisaTypeIds":null,"VisaSubTypeIds":null,"MissionId":"d133459a-6482-45ed-bd00-5ff32aa8b71b"},{"Id":"4721","Name":"Shenzhen","Code":"SHENZHEN","VisaTypeIds":null,"VisaSubTypeIds":null,"MissionId":"3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},{"Id":"4722","Name":"Changsha","Code":"CHANGSHA","VisaTypeIds":null,"VisaSubTypeIds":null,"MissionId":"3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},{"Id":"4723","Name":"Shanghai","Code":"SHANGHAI","VisaTypeIds":null,"VisaSubTypeIds":null,"MissionId":"235b19fd-9fce-438f-be0a-18275fd0b64d"},{"Id":"4724","Name":"Fuzhou","Code":"FUZHOU","VisaTypeIds":null,"VisaSubTypeIds":null,"MissionId":"3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},{"Id":"4725","Name":"Beijing","Code":"BEIJING","VisaTypeIds":null,"VisaSubTypeIds":null,"MissionId":"d133459a-6482-45ed-bd00-5ff32aa8b71b"},{"Id":"4726","Name":"Guangzhou","Code":"GUANGZHOU","VisaTypeIds":null,"VisaSubTypeIds":null,"MissionId":"3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},{"Id":"4727","Name":"Kunming","Code":"KUNMING","VisaTypeIds":null,"VisaSubTypeIds":null,"MissionId":"59bba02e-559e-49d3-9933-839d1b1588ec"},{"Id":"4728","Name":"Hangzhou","Code":"HANGZHOU","VisaTypeIds":null,"VisaSubTypeIds":null,"MissionId":"235b19fd-9fce-438f-be0a-18275fd0b64d"},{"Id":"4729","Name":"Nanjing","Code":"NANJING","VisaTypeIds":null,"VisaSubTypeIds":null,"MissionId":"235b19fd-9fce-438f-be0a-18275fd0b64d"},{"Id":"4730","Name":"Chengdu","Code":"CHENGDU","VisaTypeIds":null,"VisaSubTypeIds":null,"MissionId":"59bba02e-559e-49d3-9933-839d1b1588ec"}];
    var visasubIdData =[{"Id":"4379","Name":"EXT Visa(Residentes(Robo, Extravío tarjeta))","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"EXT_VISA"},{"Id":"4380","Name":"TEL Visa(Teletrabajadores internacionales)","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"TEL_VISA"},{"Id":"4381","Name":"RES VISA(Visado de residencia no lucrativa)","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"RES_VISA"},{"Id":"4382","Name":"SSU Visa(Visados estudios de 90 a 180 días)","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"SSU_VISA"},{"Id":"4383","Name":"ESA Visa","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"ESA_VISA"},{"Id":"4384","Name":"RFI Visa","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"RFI_VISA"},{"Id":"4385","Name":"Business/Professional Training ","Value":"3164","Code":"WEB_BLS","DepartmentOwnerUserId":"BUSINESS"},{"Id":"4386","Name":"RFK Visa(Visado de reagrupación familiar en régimen general)","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"RFK_VISA"},{"Id":"4387","Name":"TRA Visa(Trabajo y residencia cuenta ajena)","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"TRA_VISA"},{"Id":"4388","Name":"TRP(Trabajo y residencia cuenta propia)","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"TRT_VISA_VAC"},{"Id":"4389","Name":"SLU Visa(Visados estudios de más de 180 días)","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"SLU_VISA"},{"Id":"4391","Name":"TRA Visa","Value":null,"Code":"WEB_BLS","DepartmentOwnerUserId":"TRA_VISA"},{"Id":"4392","Name":"ESC Visa","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"ESC_VISA"},{"Id":"4396","Name":"LEY14 Visa(Trabajo y Residencia)","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"LEY14_VISA"},{"Id":"4398","Name":"Tourism","Value":"3164","Code":"WEB_BLS","DepartmentOwnerUserId":"TOURISM"},{"Id":"4399","Name":"Visiting family or friends ","Value":"3164","Code":"WEB_BLS","DepartmentOwnerUserId":"VISITING_FAMILY"},{"Id":"4400","Name":"Transit(for seamen)","Value":"3164","Code":"WEB_BLS","DepartmentOwnerUserId":"TRANSIT"},{"Id":"4401","Name":"Cultural reasons","Value":"3164","Code":"WEB_BLS","DepartmentOwnerUserId":"CULTURAL_REASONS"},{"Id":"4402","Name":"RLD(Visado para recuperar la residencia  de larga duración o de larga duración-UE)","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"RLD_VISA_VAC"},{"Id":"4403","Name":"RES VISA ","Value":null,"Code":"WEB_BLS","DepartmentOwnerUserId":"RES_VISA"},{"Id":"4405","Name":"RFK Visa","Value":null,"Code":"WEB_BLS","DepartmentOwnerUserId":"RFK_VISA"},{"Id":"4406","Name":"SLU Visa","Value":null,"Code":"WEB_BLS","DepartmentOwnerUserId":"SLU_VISA"},{"Id":"4407","Name":"SSU Visa","Value":null,"Code":"WEB_BLS","DepartmentOwnerUserId":"SSU_VISA"},{"Id":"4408","Name":"EXT Visa","Value":null,"Code":"WEB_BLS","DepartmentOwnerUserId":"EXT_VISA"},{"Id":"4409","Name":"LEY14 Visa","Value":null,"Code":"WEB_BLS","DepartmentOwnerUserId":"LEY14_VISA"},{"Id":"4410","Name":"ESC Visa","Value":null,"Code":"WEB_BLS","DepartmentOwnerUserId":"ESC_VISA"},{"Id":"4412","Name":"RSA Visa ","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"RSA_VISA"},{"Id":"4413","Name":"Study","Value":"3164","Code":"WEB_BLS","DepartmentOwnerUserId":"SCHENGEN_STUDY"},{"Id":"4416","Name":"TRP - Embassy","Value":"3163","Code":"WEB_EMBASSY","DepartmentOwnerUserId":"TRT_VISA"},{"Id":"4417","Name":"RLD - Embassy","Value":"3163","Code":"WEB_EMBASSY","DepartmentOwnerUserId":"RLD_VISA"},{"Id":"4418","Name":"RIV Visa ","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"RIV_VISA"},{"Id":"4419","Name":"PFK Visa ","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"PFK_VISA"},{"Id":"4420","Name":"TRE Visa(Excepciones autorización  de trabajo)","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"TRE_VISA"},{"Id":"4421","Name":"TAC Visa(Res.Profes.Altamente cualificado (LE))","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"TAC_VISA"},{"Id":"4422","Name":"RIN Visa(Visado de investigador)","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"RIN_VISA"},{"Id":"4423","Name":"TTI Visa(Res. Traslado Intraempresarial(LE))","Value":"3163","Code":"WEB_BLS","DepartmentOwnerUserId":"TTI_VISA"}];
    var redirect = '';
    var visaIdData =[{"Id":"3163","Name":"National Visa","VisaTypeCode":"NATIONAL_VISA","AppointmentSource":"WEB_BLS"},{"Id":"3164","Name":"Schengen Visa","VisaTypeCode":"SCHENGEN_VISA","AppointmentSource":"WEB_BLS"}];
    var applicantsNoData=[{"Id":"6876","Name":"2 Members","Value":"6876"},{"Id":"6877","Name":"3 Members","Value":"6877"},{"Id":"6878","Name":"4 Members","Value":"6878"},{"Id":"6879","Name":"5 Members","Value":"6879"},{"Id":"6880","Name":"6 Members","Value":"6880"}];
    var categoryData=[{"Id":"3536","Name":"Normal","Code":"CATEGORY_NORMAL","LegalEntityId":"4716","MissionId":"d133459a-6482-45ed-bd00-5ff32aa8b71b"},{"Id":"3537","Name":"Normal","Code":"CATEGORY_NORMAL","LegalEntityId":"4717","MissionId":"d133459a-6482-45ed-bd00-5ff32aa8b71b"},{"Id":"3538","Name":"Normal","Code":"CATEGORY_NORMAL","LegalEntityId":"4718","MissionId":"d133459a-6482-45ed-bd00-5ff32aa8b71b"},{"Id":"3539","Name":"Normal","Code":"CATEGORY_NORMAL","LegalEntityId":"4719","MissionId":"59bba02e-559e-49d3-9933-839d1b1588ec"},{"Id":"3540","Name":"Normal","Code":"CATEGORY_NORMAL","LegalEntityId":"4720","MissionId":"d133459a-6482-45ed-bd00-5ff32aa8b71b"},{"Id":"3541","Name":"Normal","Code":"CATEGORY_NORMAL","LegalEntityId":"4721","MissionId":"3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},{"Id":"3542","Name":"Normal","Code":"CATEGORY_NORMAL","LegalEntityId":"4722","MissionId":"3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},{"Id":"3543","Name":"Normal","Code":"CATEGORY_NORMAL","LegalEntityId":"4723","MissionId":"235b19fd-9fce-438f-be0a-18275fd0b64d"},{"Id":"3544","Name":"Normal","Code":"CATEGORY_NORMAL","LegalEntityId":"4724","MissionId":"3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},{"Id":"3545","Name":"Normal","Code":"CATEGORY_NORMAL","LegalEntityId":"4725","MissionId":"d133459a-6482-45ed-bd00-5ff32aa8b71b"},{"Id":"3546","Name":"Normal","Code":"CATEGORY_NORMAL","LegalEntityId":"4726","MissionId":"3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},{"Id":"3547","Name":"Normal","Code":"CATEGORY_NORMAL","LegalEntityId":"4727","MissionId":"59bba02e-559e-49d3-9933-839d1b1588ec"},{"Id":"3548","Name":"Normal","Code":"CATEGORY_NORMAL","LegalEntityId":"4728","MissionId":"235b19fd-9fce-438f-be0a-18275fd0b64d"},{"Id":"3549","Name":"Normal","Code":"CATEGORY_NORMAL","LegalEntityId":"4729","MissionId":"235b19fd-9fce-438f-be0a-18275fd0b64d"},{"Id":"3550","Name":"Normal","Code":"CATEGORY_NORMAL","LegalEntityId":"4730","MissionId":"59bba02e-559e-49d3-9933-839d1b1588ec"},{"Id":"3551","Name":"Premium","Code":"CATEGORY_PREMIUM","LegalEntityId":"4723","MissionId":"235b19fd-9fce-438f-be0a-18275fd0b64d"},{"Id":"3552","Name":"Premium","Code":"CATEGORY_PREMIUM","LegalEntityId":"4728","MissionId":"235b19fd-9fce-438f-be0a-18275fd0b64d"},{"Id":"3553","Name":"Premium","Code":"CATEGORY_PREMIUM","LegalEntityId":"4729","MissionId":"235b19fd-9fce-438f-be0a-18275fd0b64d"},{"Id":"3554","Name":"Prime Time","Code":"PRIME_TIME","LegalEntityId":"4721","MissionId":"3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},{"Id":"3555","Name":"Prime Time","Code":"PRIME_TIME","LegalEntityId":"4722","MissionId":"3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},{"Id":"3556","Name":"Prime Time","Code":"PRIME_TIME","LegalEntityId":"4724","MissionId":"3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},{"Id":"3557","Name":"Prime Time","Code":"PRIME_TIME","LegalEntityId":"4726","MissionId":"3ee1ef97-553a-4f8a-89c3-025cfc38e91b"},{"Id":"3558","Name":"Prime Time","Code":"PRIME_TIME","LegalEntityId":"4727","MissionId":"59bba02e-559e-49d3-9933-839d1b1588ec"}];
    var missionData=[{"Id":"8688","Name":"Consulate - Beijing","Code":"CONSULATE_BEIJING"},{"Id":"8689","Name":"Consulate-Shanghai","Code":"CONSULATE_SHANGHAI"},{"Id":"8690","Name":"Consulate-Guangzhou","Code":"CONSULATE_GUANGZHOU"},{"Id":"8691","Name":"Consulate-Chengdu","Code":"CONSULATE_CHENGDU"}];
</script>
<div id="div-main" class="row pb-3 pl-5 pr-5">
        <div class="d-none d-sm-block col-md-2">
        </div>
        <div class="shadow rounded-3 p-3 col-md-3 d-sm-12">
            <div class="text-center col-12">
                <a href="https://spain.blscn.cn/CHN/Appointment/index.html">
                    <img class="h-50px mb-2" src="./上海-选类型-一组_files/logo.png" alt="logo">
                </a>
            </div>
            <form method="post" action="https://spain.blscn.cn/CHN/Appointment/VisaType">
                <div class="validation-summary text-danger mb-3 validation-summary-valid" data-valmsg-summary="true"><ul><li style="display:none"></li>
</ul></div>
                <div class="pt-2">
<div class="mb-3 pkego efvetddl zdsux ciiesh aekwfwgh uladbbwmr fbwbwhud cgvaacpx whqrvr bahoglre ampgzpw bbbwgi fnpssl jcfqxi wqwut iilzzaa btlwdf owzdiunk dimwv enzxhzx ikhhzz wqswh pgygvaq cqlidbxn mt-6">
	                                <label class="form-label" for="gpcmwplx" id="gpcmwplx_label">Visa Type<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="gpcmwplx_listbox" aria-labelledby="gpcmwplx_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="pfd20128-3cf1-4fde-809c-b0e4f72d739b" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="gpcmwplx" name="gpcmwplx" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div><div class="mb-3 jcfqxi ampgzpw scsnjgmjr whqrvr efvetddl pgygvaq cqlidbxn bahoglre bbbwgi owzdiunk fkxpgurmo cgvaacpx fnpssl enzxhzx qvvjti wqwut ikhhzz aekwfwgh fbwbwhud pkego btlwdf iilzzaa dimwv uladbbwmr hpcgxrbd ciiesh zctzv wqswh zdsux mt-6">
	                                <label class="form-label" for="mpnkgnn" id="mpnkgnn_label">Visa Type<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="mpnkgnn_listbox" aria-labelledby="mpnkgnn_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="y886e87d-ac99-4b0b-879f-994a7ac81a35" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="mpnkgnn" name="mpnkgnn" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div><div class="mb-3 ikhhzz efvetddl ampgzpw bbbwgi cqlidbxn owzdiunk iilzzaa pgygvaq pkego wqswh btlwdf whqrvr fnpssl ciiesh uladbbwmr fbwbwhud bahoglre dimwv mt-6">
	                                <label class="form-label" for="etfhj" id="etfhj_label">Visa Type<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="etfhj_listbox" aria-labelledby="etfhj_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="h671b99b-a392-44b3-a936-4b894f3ed030" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="etfhj" name="etfhj" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div><div class="mb-3 hpcgxrbd uladbbwmr jcfqxi zctzv enzxhzx dimwv bbbwgi iilzzaa pkego pgygvaq zdsux aekwfwgh bahoglre owzdiunk fbwbwhud cgvaacpx ampgzpw iccuorfl btlwdf wqswh ikhhzz fnpssl whqrvr wqwut fkxpgurmo efvetddl cqlidbxn mt-6">
	                                <label class="form-label" for="xesba" id="xesba_label">Visa Type<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="true" tabindex="0" aria-owns="xesba_listbox" aria-labelledby="xesba_label" aria-live="polite" aria-disabled="false" aria-readonly="false" aria-busy="false" aria-activedescendant="i7bcfb20-9783-4781-ac42-69771a505721" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="i7bcfb20-9783-4781-ac42-69771a505721" unselectable="on" role="option" aria-selected="true" class="k-input">Schengen Visa</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="xesba" name="xesba" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div><div class="mb-3 owzdiunk wqwut scsnjgmjr sfaoq dxljy wusvbnxxi qvvjti iilzzaa bahoglre jlnyfhxgj efvetddl bhegr fbwbwhud yemihq gviyvffq qqsnjuguk whqrvr denjenyj rewlf cgvaacpx yzzzlm ampgzpw ggmziln aekwfwgh ywxxlv ikhhzz dhdtsymr fkxpgurmo zdsux cqlidbxn pkego ciiesh enzxhzx zctzv uladbbwmr wqswh ephmfpxwj etjknuw hpcgxrbd btlwdf dimwv jcfqxi bbbwgi fnpssl kuijtnq pgygvaq zzhjgvmvp dlgpzkqfj mt-6">
	                                <label class="form-label" for="tznhkij" id="tznhkij_label">Visa Type<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="tznhkij_listbox" aria-labelledby="tznhkij_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="i91cb9d8-dd1e-4b24-a350-431cf7d9104f" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="tznhkij" name="tznhkij" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div><div class="mb-3 scsnjgmjr dimwv etjknuw uladbbwmr aekwfwgh cqlidbxn zzhjgvmvp cgvaacpx dxljy iilzzaa ampgzpw qvvjti ikhhzz yemihq qqsnjuguk kuijtnq jcfqxi efvetddl wusvbnxxi bhegr sfaoq enzxhzx wqwut bahoglre zctzv pkego wqswh ephmfpxwj ggmziln btlwdf hpcgxrbd jlnyfhxgj owzdiunk dlgpzkqfj whqrvr dhdtsymr gviyvffq ywxxlv pgygvaq fkxpgurmo denjenyj bbbwgi yzzzlm iccuorfl fnpssl fbwbwhud zdsux rewlf mt-6">
	                                <label class="form-label" for="sswcy" id="sswcy_label">Visa Sub Type<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="true" tabindex="0" aria-owns="sswcy_listbox" aria-labelledby="sswcy_label" aria-live="polite" aria-disabled="false" aria-readonly="false" aria-busy="false" aria-activedescendant="h399ac1f-9e3e-4ae7-9902-32bf016ef769" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="h399ac1f-9e3e-4ae7-9902-32bf016ef769" unselectable="on" role="option" aria-selected="true" class="k-input">Business/Professional Training </span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="sswcy" name="sswcy" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div><div class="mb-3 pgygvaq uladbbwmr ciiesh rewlf qqsnjuguk bahoglre zzhjgvmvp dhdtsymr gviyvffq whqrvr wqswh wusvbnxxi bhegr fkxpgurmo bbbwgi ephmfpxwj ggmziln enzxhzx cqlidbxn etjknuw fbwbwhud dlgpzkqfj cgvaacpx owzdiunk pkego sfaoq denjenyj qvvjti ikhhzz ampgzpw btlwdf dxljy kuijtnq aekwfwgh jcfqxi zctzv yemihq scsnjgmjr yzzzlm jlnyfhxgj fnpssl hpcgxrbd dimwv ywxxlv netcklxlm iilzzaa wqwut zdsux efvetddl mt-6">
	                                <label class="form-label" for="hcgflcvkb" id="hcgflcvkb_label">Visa Sub Type<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="hcgflcvkb_listbox" aria-labelledby="hcgflcvkb_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="i18acff5-1e8a-404d-bbd4-0a40736b4070" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="hcgflcvkb" name="hcgflcvkb" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div><div class="mb-3 bahoglre gviyvffq whqrvr fbwbwhud mvjywuy tegewmi wusvbnxxi ciiesh gjdvrfnx uladbbwmr iczver pgygvaq iilzzaa wqswh fkxpgurmo qphaxibbk dmhit cqlidbxn ampgzpw zrjahw efvetddl mvwxgntg scsnjgmjr owzdiunk qvvjti ephmfpxwj dxljy zdsux qqsnjuguk dhdtsymr bhegr dlgpzkqfj enzxhzx wqwut btlwdf ggmziln dimwv ogixiwup netcklxlm brqhrg pkego eovqys etjknuw rnqyr zctzv denjenyj yemihq ywxxlv kuijtnq jcfqxi cgvaacpx fnpssl jlnyfhxgj vuavhjw bbbwgi ikhhzz aekwfwgh zzhjgvmvp fefirxpk sfaoq rewlf yzzzlm hpcgxrbd mt-6">
	                                <label class="form-label" for="mowfjxdv" id="mowfjxdv_label">Visa Sub Type<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="mowfjxdv_listbox" aria-labelledby="mowfjxdv_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="u0839844-7a81-494f-afb3-670661ad90fa" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="mowfjxdv" name="mowfjxdv" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div><div class="mb-3 hpcgxrbd dlgpzkqfj cgvaacpx ephmfpxwj dimwv ciiesh btlwdf gviyvffq fbwbwhud efvetddl bhegr yemihq denjenyj pkego fnpssl etjknuw kuijtnq ampgzpw wusvbnxxi qqsnjuguk enzxhzx jcfqxi zzhjgvmvp zdsux iilzzaa ywxxlv wqswh scsnjgmjr whqrvr rewlf wqwut fkxpgurmo pgygvaq dxljy zctzv yzzzlm sfaoq bbbwgi ikhhzz bahoglre qvvjti owzdiunk jlnyfhxgj cqlidbxn uladbbwmr aekwfwgh mt-6">
	                                <label class="form-label" for="wycprkap" id="wycprkap_label">Visa Sub Type<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="wycprkap_listbox" aria-labelledby="wycprkap_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="xd9f07df-d434-4801-b583-ec0e8587cd2f" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="wycprkap" name="wycprkap" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div><div class="mb-3 yzzzlm hpcgxrbd iilzzaa wqwut bahoglre bbbwgi dimwv ciiesh fnpssl gviyvffq owzdiunk fbwbwhud aekwfwgh cqlidbxn sfaoq zctzv cgvaacpx enzxhzx jcfqxi whqrvr ephmfpxwj wqswh qqsnjuguk zzhjgvmvp qvvjti rewlf yemihq ikhhzz uladbbwmr wusvbnxxi pgygvaq ampgzpw efvetddl btlwdf scsnjgmjr fkxpgurmo zdsux pkego dlgpzkqfj mt-6">
	                                <label class="form-label" for="pntczgh" id="pntczgh_label">Visa Sub Type<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="pntczgh_listbox" aria-labelledby="pntczgh_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="x83e378e-bbe4-41e3-be8b-18731b329bff" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="pntczgh" name="pntczgh" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div><div style="display:none;" id="div-tkrfk"><div class="mb-3 fbwbwhud cgvaacpx zdsux qqsnjuguk dlgpzkqfj yzzzlm kuijtnq denjenyj enzxhzx bhegr bbbwgi btlwdf hpcgxrbd cqlidbxn efvetddl jlnyfhxgj owzdiunk ampgzpw yemihq pgygvaq qvvjti scsnjgmjr pkego gviyvffq wqwut ikhhzz etjknuw whqrvr rewlf zctzv fnpssl ephmfpxwj iilzzaa wqswh fkxpgurmo wusvbnxxi ggmziln sfaoq dimwv ywxxlv ciiesh bahoglre jcfqxi uladbbwmr zzhjgvmvp aekwfwgh dxljy mt-3">
	                                <label class="form-label" for="tkrfk" id="tkrfk_label">Location<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="tkrfk_listbox" aria-labelledby="tkrfk_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="o8d99bdb-0301-4d39-b78d-2115a4d3165c" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="tkrfk" name="tkrfk" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div></div><div style="display:none;" id="div-upiayn"><div class="mb-3 zdsux fkxpgurmo iilzzaa ampgzpw hpcgxrbd bahoglre jcfqxi pkego fbwbwhud wqswh cqlidbxn ikhhzz enzxhzx owzdiunk btlwdf dimwv uladbbwmr aekwfwgh ciiesh qvvjti wqwut fnpssl cgvaacpx whqrvr scsnjgmjr zctzv pgygvaq bbbwgi efvetddl mt-3">
	                                <label class="form-label" for="upiayn" id="upiayn_label">Location<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="upiayn_listbox" aria-labelledby="upiayn_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="nddd6c01-6c44-462d-8b96-5bcb89854e1f" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="upiayn" name="upiayn" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div></div><div style="" id="div-jqeixm"><div class="mb-3 scsnjgmjr bahoglre aekwfwgh owzdiunk pkego efvetddl ampgzpw iilzzaa jcfqxi qvvjti enzxhzx cgvaacpx ikhhzz uladbbwmr hpcgxrbd whqrvr gviyvffq fnpssl yemihq fbwbwhud wqswh zctzv rewlf dimwv wqwut pgygvaq bbbwgi cqlidbxn fkxpgurmo btlwdf zdsux iccuorfl mt-3">
	                                <label class="form-label" for="jqeixm" id="jqeixm_label">Location<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="true" tabindex="0" aria-owns="jqeixm_listbox" aria-labelledby="jqeixm_label" aria-live="polite" aria-disabled="false" aria-readonly="false" aria-busy="false" aria-activedescendant="a7edfd13-185e-49e2-b038-861f4f451aa3" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="a7edfd13-185e-49e2-b038-861f4f451aa3" unselectable="on" role="option" aria-selected="true" class="k-input">Shanghai</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="jqeixm" name="jqeixm" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div></div><div style="display:none;" id="div-dsmdx"><div class="mb-3 ephmfpxwj bhegr netcklxlm wqwut aekwfwgh yzzzlm denjenyj rewlf qvvjti qqsnjuguk owzdiunk bahoglre dlgpzkqfj cgvaacpx efvetddl fnpssl yemihq btlwdf scsnjgmjr ciiesh gviyvffq jcfqxi wqswh mvwxgntg iilzzaa ywxxlv dimwv wusvbnxxi sfaoq whqrvr fbwbwhud pgygvaq hpcgxrbd etjknuw uladbbwmr zctzv dhdtsymr bbbwgi ikhhzz pkego enzxhzx kuijtnq rnqyr fkxpgurmo zzhjgvmvp cqlidbxn ampgzpw jlnyfhxgj ggmziln dxljy zdsux mt-3">
	                                <label class="form-label" for="dsmdx" id="dsmdx_label">Location<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="dsmdx_listbox" aria-labelledby="dsmdx_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="oa8b15fe-7bc9-47e7-9ca6-1edf6248648f" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="dsmdx" name="dsmdx" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div></div><div style="display:none;" id="div-phvheo"><div class="mb-3 hpcgxrbd dimwv kuijtnq dxljy yemihq jcfqxi scsnjgmjr fbwbwhud rnqyr owzdiunk ciiesh denjenyj zdsux enzxhzx vuavhjw yzzzlm bbbwgi efvetddl fobhuus rewlf aekwfwgh zrjahw qqsnjuguk wqwut verqp zzhjgvmvp netcklxlm ywxxlv qphaxibbk dmhit whqrvr jlnyfhxgj zctzv btlwdf pkego gjdvrfnx iilzzaa bahoglre qvvjti ogixiwup wqswh wusvbnxxi ampgzpw tegewmi fnpssl etjknuw bhegr cgvaacpx pgygvaq iczver mvjywuy sfaoq brqhrg gviyvffq fefirxpk dlgpzkqfj eovqys fkxpgurmo cqlidbxn dhdtsymr ikhhzz ephmfpxwj uladbbwmr ggmziln mvwxgntg mt-3">
	                                <label class="form-label" for="phvheo" id="phvheo_label">Location<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="phvheo_listbox" aria-labelledby="phvheo_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="a7bee197-e558-4998-9326-dddf1f0df187" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="phvheo" name="phvheo" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div></div><div style="display:none;" id="div-nrtmw"><div class="mb-3 pkego bbbwgi aekwfwgh cqlidbxn efvetddl ikhhzz fbwbwhud bahoglre cgvaacpx owzdiunk whqrvr wqswh iccuorfl pgygvaq iilzzaa btlwdf fnpssl ampgzpw dimwv uladbbwmr mt-4">
	                                <label class="form-label" for="nrtmw" id="nrtmw_label">Mission<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="nrtmw_listbox" aria-labelledby="nrtmw_label" aria-live="polite" aria-disabled="false" aria-readonly="false" aria-busy="false" aria-activedescendant="saabff27-773c-46da-8f30-e218c743aae6" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="saabff27-773c-46da-8f30-e218c743aae6" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="nrtmw" name="nrtmw" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div></div><div style="display:none;" id="div-jdqcu"><div class="mb-3 scsnjgmjr bahoglre dimwv qvvjti efvetddl jcfqxi pkego fbwbwhud wqwut wqswh ikhhzz bbbwgi owzdiunk hpcgxrbd fnpssl ciiesh zdsux whqrvr fkxpgurmo enzxhzx zctzv pgygvaq iilzzaa aekwfwgh btlwdf cgvaacpx gviyvffq ampgzpw cqlidbxn uladbbwmr mt-4">
	                                <label class="form-label" for="jdqcu" id="jdqcu_label">Mission<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="jdqcu_listbox" aria-labelledby="jdqcu_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="v556fc3d-3261-4f9e-88c5-88d7f690e6f8" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="jdqcu" name="jdqcu" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div></div><div style="display:none;" id="div-djkksbhsf"><div class="mb-3 whqrvr efvetddl ampgzpw fnpssl ciiesh pgygvaq btlwdf dimwv bbbwgi cqlidbxn wqswh fbwbwhud uladbbwmr pkego ikhhzz bahoglre iilzzaa owzdiunk mt-4">
	                                <label class="form-label" for="djkksbhsf" id="djkksbhsf_label">Mission<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="djkksbhsf_listbox" aria-labelledby="djkksbhsf_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="oea4a078-c517-4372-87af-7c2e0db9ebed" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="djkksbhsf" name="djkksbhsf" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div></div><div style="display:none;" id="div-sgdmhussm"><div class="mb-3 pgygvaq efvetddl zdsux wqswh ampgzpw iilzzaa bahoglre btlwdf cqlidbxn fkxpgurmo ciiesh whqrvr wqwut pkego cgvaacpx qvvjti fnpssl aekwfwgh dimwv owzdiunk ikhhzz zctzv hpcgxrbd bbbwgi fbwbwhud uladbbwmr jcfqxi enzxhzx mt-4">
	                                <label class="form-label" for="sgdmhussm" id="sgdmhussm_label">Mission<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="sgdmhussm_listbox" aria-labelledby="sgdmhussm_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="l897427f-db4a-4fe4-b050-10304212c210" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="sgdmhussm" name="sgdmhussm" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div></div><div style="display:none;" id="div-mappywgy"><div class="mb-3 ikhhzz efvetddl whqrvr bahoglre ampgzpw fnpssl ciiesh pgygvaq fbwbwhud iilzzaa dimwv bbbwgi cqlidbxn wqswh pkego mt-4">
	                                <label class="form-label" for="mappywgy" id="mappywgy_label">Mission<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="mappywgy_listbox" aria-labelledby="mappywgy_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="m4ccf6c6-4c14-4c0c-8abb-e08c65244c19" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="mappywgy" name="mappywgy" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div></div><div class="mb-3 fkxpgurmo rewlf cgvaacpx dxljy wqwut jlnyfhxgj sfaoq ephmfpxwj jcfqxi netcklxlm gviyvffq yemihq etjknuw aekwfwgh enzxhzx brqhrg owzdiunk ggmziln wusvbnxxi rnqyr bbbwgi bhegr fnpssl cqlidbxn tegewmi denjenyj fbwbwhud yzzzlm bahoglre hpcgxrbd efvetddl mvwxgntg ikhhzz dhdtsymr kuijtnq ampgzpw zctzv uladbbwmr wqswh whqrvr zrjahw dlgpzkqfj zdsux zzhjgvmvp ogixiwup pgygvaq ywxxlv vuavhjw dimwv btlwdf qqsnjuguk scsnjgmjr qvvjti ciiesh iilzzaa pkego mt-6">
                            <label class="form-label">Appointment For<span class="text-danger">*</span></label>
                            <div class="d-flex gap-4">
                                <div class="form-check radio-bg-light">
                                    <input type="radio" name="afadxuvn" checked="checked" class="form-check-input" value="Individual" id="selfadxuvn" onclick="OnAppointmentForChange(event,&#39;adxuvn&#39;);">
                                    <label class="form-check-label" for="selfadxuvn" style="margin-left: 22px;">
                                       Individual
                                    </label>
                                </div>
                               <div class="form-check radio-bg-light">
                                    <input type="radio" name="afadxuvn" class="form-check-input" value="Family" id="familyadxuvn" onclick="OnAppointmentForChange(event,&#39;adxuvn&#39;);">
                                    <label class="form-check-label" for="familyadxuvn" style="margin-left: 22px;">
                                        Family/Group
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3 fkxpgurmo rewlf cgvaacpx dxljy wqwut jlnyfhxgj sfaoq ephmfpxwj jcfqxi netcklxlm gviyvffq yemihq etjknuw aekwfwgh enzxhzx brqhrg owzdiunk ggmziln wusvbnxxi rnqyr bbbwgi bhegr fnpssl cqlidbxn tegewmi denjenyj fbwbwhud yzzzlm bahoglre hpcgxrbd efvetddl mvwxgntg ikhhzz dhdtsymr kuijtnq ampgzpw zctzv uladbbwmr wqswh whqrvr zrjahw dlgpzkqfj zdsux zzhjgvmvp ogixiwup pgygvaq ywxxlv vuavhjw dimwv btlwdf qqsnjuguk scsnjgmjr qvvjti ciiesh iilzzaa pkego mt-6">
                            <div id="membersadxuvn" style="display:none;">
	                            <label class="form-label" for="anadxuvn" id="anadxuvn_label">Number Of Members<span class="required">*</span></label>
	                            <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="anadxuvn_listbox" aria-labelledby="anadxuvn_label" aria-live="polite" aria-disabled="false" aria-readonly="false" aria-busy="false" aria-activedescendant="nf2ab7f3-7a49-436f-a56c-cb03771a9d0d" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="nf2ab7f3-7a49-436f-a56c-cb03771a9d0d" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="anadxuvn" name="anadxuvn" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                <script>
                                        $(document).ready(function () {
                                            $("#anadxuvn").kendoDropDownList({
                                                optionLabel: "--Select--",
                                                dataTextField: "Name",
                                                dataValueField: "Value",
                                                filter: "contains",
                                                dataSource: applicantsNoData,
                                                open: onDrpOpen,
                                                close: onDrpClose,
                                                select: onDrpSelect
                                            });
                                        });
                                 </script>
                             </div>              
                        </div><div class="mb-3 zctzv cqlidbxn iilzzaa ciiesh aekwfwgh btlwdf jcfqxi gviyvffq fkxpgurmo qvvjti wqswh whqrvr dimwv ampgzpw cgvaacpx owzdiunk pkego zdsux hpcgxrbd efvetddl bahoglre uladbbwmr bbbwgi pgygvaq enzxhzx ikhhzz wqwut fbwbwhud yemihq fnpssl scsnjgmjr mt-6">
                            <label class="form-label">Appointment For<span class="text-danger">*</span></label>
                            <div class="d-flex gap-4">
                                <div class="form-check radio-bg-light">
                                    <input type="radio" name="afhgshaun" checked="checked" class="form-check-input" value="Individual" id="selfhgshaun" onclick="OnAppointmentForChange(event,&#39;hgshaun&#39;);">
                                    <label class="form-check-label" for="selfhgshaun" style="margin-left: 22px;">
                                       Individual
                                    </label>
                                </div>
                               <div class="form-check radio-bg-light">
                                    <input type="radio" name="afhgshaun" class="form-check-input" value="Family" id="familyhgshaun" onclick="OnAppointmentForChange(event,&#39;hgshaun&#39;);">
                                    <label class="form-check-label" for="familyhgshaun" style="margin-left: 22px;">
                                        Family/Group
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3 zctzv cqlidbxn iilzzaa ciiesh aekwfwgh btlwdf jcfqxi gviyvffq fkxpgurmo qvvjti wqswh whqrvr dimwv ampgzpw cgvaacpx owzdiunk pkego zdsux hpcgxrbd efvetddl bahoglre uladbbwmr bbbwgi pgygvaq enzxhzx ikhhzz wqwut fbwbwhud yemihq fnpssl scsnjgmjr mt-6">
                            <div id="membershgshaun" style="display:none;">
	                            <label class="form-label" for="anhgshaun" id="anhgshaun_label">Number Of Members<span class="required">*</span></label>
	                            <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="anhgshaun_listbox" aria-labelledby="anhgshaun_label" aria-live="polite" aria-disabled="false" aria-readonly="false" aria-busy="false" aria-activedescendant="tbd705b1-c998-4573-a455-eb92524a34a1" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="tbd705b1-c998-4573-a455-eb92524a34a1" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="anhgshaun" name="anhgshaun" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                <script>
                                        $(document).ready(function () {
                                            $("#anhgshaun").kendoDropDownList({
                                                optionLabel: "--Select--",
                                                dataTextField: "Name",
                                                dataValueField: "Value",
                                                filter: "contains",
                                                dataSource: applicantsNoData,
                                                open: onDrpOpen,
                                                close: onDrpClose,
                                                select: onDrpSelect
                                            });
                                        });
                                 </script>
                             </div>              
                        </div><div class="mb-3 aekwfwgh cgvaacpx bahoglre cqlidbxn dimwv jcfqxi pgygvaq whqrvr ciiesh iilzzaa zctzv zdsux wqwut bbbwgi hpcgxrbd enzxhzx ampgzpw owzdiunk wqswh pkego fbwbwhud efvetddl fnpssl uladbbwmr btlwdf ikhhzz mt-6">
                            <label class="form-label">Appointment For<span class="text-danger">*</span></label>
                            <div class="d-flex gap-4">
                                <div class="form-check radio-bg-light">
                                    <input type="radio" name="afszjxtz" checked="checked" class="form-check-input" value="Individual" id="selfszjxtz" onclick="OnAppointmentForChange(event,&#39;szjxtz&#39;);">
                                    <label class="form-check-label" for="selfszjxtz" style="margin-left: 22px;">
                                       Individual
                                    </label>
                                </div>
                               <div class="form-check radio-bg-light">
                                    <input type="radio" name="afszjxtz" class="form-check-input" value="Family" id="familyszjxtz" onclick="OnAppointmentForChange(event,&#39;szjxtz&#39;);">
                                    <label class="form-check-label" for="familyszjxtz" style="margin-left: 22px;">
                                        Family/Group
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3 aekwfwgh cgvaacpx bahoglre cqlidbxn dimwv jcfqxi pgygvaq whqrvr ciiesh iilzzaa zctzv zdsux wqwut bbbwgi hpcgxrbd enzxhzx ampgzpw owzdiunk wqswh pkego fbwbwhud efvetddl fnpssl uladbbwmr btlwdf ikhhzz mt-6">
                            <div id="membersszjxtz" style="display:none;">
	                            <label class="form-label" for="anszjxtz" id="anszjxtz_label">Number Of Members<span class="required">*</span></label>
	                            <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="anszjxtz_listbox" aria-labelledby="anszjxtz_label" aria-live="polite" aria-disabled="false" aria-readonly="false" aria-busy="false" aria-activedescendant="fe61d776-17fd-4c94-b792-19d46f561ca2" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="fe61d776-17fd-4c94-b792-19d46f561ca2" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="anszjxtz" name="anszjxtz" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                <script>
                                        $(document).ready(function () {
                                            $("#anszjxtz").kendoDropDownList({
                                                optionLabel: "--Select--",
                                                dataTextField: "Name",
                                                dataValueField: "Value",
                                                filter: "contains",
                                                dataSource: applicantsNoData,
                                                open: onDrpOpen,
                                                close: onDrpClose,
                                                select: onDrpSelect
                                            });
                                        });
                                 </script>
                             </div>              
                        </div><div class="mb-3 btlwdf ampgzpw cqlidbxn dimwv bahoglre efvetddl iilzzaa pgygvaq fnpssl bbbwgi fbwbwhud whqrvr ciiesh ikhhzz wqswh pkego mt-6">
                            <label class="form-label">Appointment For<span class="text-danger">*</span></label>
                            <div class="d-flex gap-4">
                                <div class="form-check radio-bg-light">
                                    <input type="radio" name="afqeppq" checked="checked" class="form-check-input" value="Individual" id="selfqeppq" onclick="OnAppointmentForChange(event,&#39;qeppq&#39;);">
                                    <label class="form-check-label" for="selfqeppq" style="margin-left: 22px;">
                                       Individual
                                    </label>
                                </div>
                               <div class="form-check radio-bg-light">
                                    <input type="radio" name="afqeppq" class="form-check-input" value="Family" id="familyqeppq" onclick="OnAppointmentForChange(event,&#39;qeppq&#39;);">
                                    <label class="form-check-label" for="familyqeppq" style="margin-left: 22px;">
                                        Family/Group
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3 btlwdf ampgzpw cqlidbxn dimwv bahoglre efvetddl iilzzaa pgygvaq fnpssl bbbwgi fbwbwhud whqrvr ciiesh ikhhzz wqswh pkego mt-6">
                            <div id="membersqeppq" style="display:none;">
	                            <label class="form-label" for="anqeppq" id="anqeppq_label">Number Of Members<span class="required">*</span></label>
	                            <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="anqeppq_listbox" aria-labelledby="anqeppq_label" aria-live="polite" aria-disabled="false" aria-readonly="false" aria-busy="false" aria-activedescendant="rb33acad-4a69-4803-8492-6eed7a1b6753" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="rb33acad-4a69-4803-8492-6eed7a1b6753" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="anqeppq" name="anqeppq" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                <script>
                                        $(document).ready(function () {
                                            $("#anqeppq").kendoDropDownList({
                                                optionLabel: "--Select--",
                                                dataTextField: "Name",
                                                dataValueField: "Value",
                                                filter: "contains",
                                                dataSource: applicantsNoData,
                                                open: onDrpOpen,
                                                close: onDrpClose,
                                                select: onDrpSelect
                                            });
                                        });
                                 </script>
                             </div>              
                        </div><div class="mb-3 fbwbwhud wqwut zdsux wqswh btlwdf owzdiunk ampgzpw iccuorfl uladbbwmr dlgpzkqfj aekwfwgh whqrvr cqlidbxn qvvjti hpcgxrbd rewlf cgvaacpx gviyvffq dimwv yzzzlm enzxhzx fkxpgurmo iilzzaa efvetddl ikhhzz zctzv bbbwgi fnpssl pkego bahoglre scsnjgmjr pgygvaq yemihq jcfqxi qqsnjuguk mt-6">
                            <label class="form-label">Appointment For<span class="text-danger">*</span></label>
                            <div class="d-flex gap-4">
                                <div class="form-check radio-bg-light">
                                    <input type="radio" name="afhaibcg" checked="checked" class="form-check-input" value="Individual" id="selfhaibcg" onclick="OnAppointmentForChange(event,&#39;haibcg&#39;);">
                                    <label class="form-check-label" for="selfhaibcg" style="margin-left: 22px;">
                                       Individual
                                    </label>
                                </div>
                               <div class="form-check radio-bg-light">
                                    <input type="radio" name="afhaibcg" class="form-check-input" value="Family" id="familyhaibcg" onclick="OnAppointmentForChange(event,&#39;haibcg&#39;);">
                                    <label class="form-check-label" for="familyhaibcg" style="margin-left: 22px;">
                                        Family/Group
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3 fbwbwhud wqwut zdsux wqswh btlwdf owzdiunk ampgzpw iccuorfl uladbbwmr dlgpzkqfj aekwfwgh whqrvr cqlidbxn qvvjti hpcgxrbd rewlf cgvaacpx gviyvffq dimwv yzzzlm enzxhzx fkxpgurmo iilzzaa efvetddl ikhhzz zctzv bbbwgi fnpssl pkego bahoglre scsnjgmjr pgygvaq yemihq jcfqxi qqsnjuguk mt-6">
                            <div id="membershaibcg" style="">
	                            <label class="form-label" for="anhaibcg" id="anhaibcg_label">Number Of Members<span class="required">*</span></label>
	                            <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="true" tabindex="0" aria-owns="anhaibcg_listbox" aria-labelledby="anhaibcg_label" aria-live="polite" aria-disabled="false" aria-readonly="false" aria-busy="false" style="width: 100%;" aria-activedescendant="qc67da5c-21c2-4976-bfeb-bdfaf02c34a6"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="qc67da5c-21c2-4976-bfeb-bdfaf02c34a6" unselectable="on" role="option" aria-selected="true" class="k-input">2 Members</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="anhaibcg" name="anhaibcg" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                <script>
                                        $(document).ready(function () {
                                            $("#anhaibcg").kendoDropDownList({
                                                optionLabel: "--Select--",
                                                dataTextField: "Name",
                                                dataValueField: "Value",
                                                filter: "contains",
                                                dataSource: applicantsNoData,
                                                open: onDrpOpen,
                                                close: onDrpClose,
                                                select: onDrpSelect
                                            });
                                        });
                                 </script>
                             </div>              
                        </div><div style="display:none;" id="div-pjirff"><div class="mb-3 ephmfpxwj tegewmi dlgpzkqfj fnpssl ogixiwup zzhjgvmvp dxljy gviyvffq fefirxpk qvvjti qphaxibbk ikhhzz yzzzlm xcfsrlmod owzdiunk scsnjgmjr jcfqxi rnqyr ywxxlv eovqys verqp wqwut rewlf cqlidbxn iblqbaqzk dmhit cgvaacpx sfaoq aekwfwgh bbbwgi pcwkf dimwv netcklxlm bahoglre ampgzpw fobhuus iczver denjenyj yodjhgnyt yemihq qqsnjuguk kuijtnq whqrvr dhdtsymr efvetddl vuavhjw fkxpgurmo mvjywuy pgygvaq etjknuw ciiesh jlnyfhxgj pkego hpcgxrbd mvwxgntg iilzzaa fbwbwhud ggmziln zrjahw bhegr uladbbwmr wqswh wusvbnxxi gjdvrfnx brqhrg zdsux btlwdf zctzv enzxhzx mt-4">
	                                <label class="form-label" for="pjirff" id="pjirff_label">Appointment Category<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="pjirff_listbox" aria-labelledby="pjirff_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="t71250f4-98ec-4f8c-83a5-6d6e44a92a05" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="pjirff" name="pjirff" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div></div><div style="" id="div-cjflcz"><div class="mb-3 cqlidbxn fnpssl iccuorfl ampgzpw ikhhzz pkego dimwv iilzzaa bahoglre wqswh efvetddl fbwbwhud whqrvr bbbwgi pgygvaq mt-4">
	                                <label class="form-label" for="cjflcz" id="cjflcz_label">Appointment Category<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="true" tabindex="0" aria-owns="cjflcz_listbox" aria-labelledby="cjflcz_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;" aria-busy="false" aria-activedescendant="m442e38b-4005-4122-ad40-f6f25068fff3"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="we4eac7b-4480-423f-94a0-22cdf68f7b94" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="cjflcz" name="cjflcz" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div></div><div style="display:none;" id="div-hglorb"><div class="mb-3 enzxhzx whqrvr ikhhzz cgvaacpx efvetddl dimwv zdsux uladbbwmr pgygvaq wqswh btlwdf cqlidbxn bbbwgi fnpssl aekwfwgh ampgzpw ciiesh fbwbwhud bahoglre pkego owzdiunk iilzzaa mt-4">
	                                <label class="form-label" for="hglorb" id="hglorb_label">Appointment Category<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="hglorb_listbox" aria-labelledby="hglorb_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="h17d59e7-235d-4121-a3e8-8072d6ca473f" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="hglorb" name="hglorb" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div></div><div style="display:none;" id="div-ovjkxw"><div class="mb-3 pkego ciiesh wqswh cgvaacpx fnpssl uladbbwmr whqrvr bahoglre dimwv efvetddl enzxhzx fbwbwhud wqwut pgygvaq fkxpgurmo ikhhzz owzdiunk aekwfwgh jcfqxi zdsux hpcgxrbd ampgzpw bbbwgi zctzv iilzzaa btlwdf cqlidbxn mt-4">
	                                <label class="form-label" for="ovjkxw" id="ovjkxw_label">Appointment Category<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="ovjkxw_listbox" aria-labelledby="ovjkxw_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="c567ab91-5f6a-4198-ba28-2bfc1c8ab9c1" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="ovjkxw" name="ovjkxw" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div></div><div style="display:none;" id="div-jzwjc"><div class="mb-3 zzhjgvmvp zrjahw btlwdf pkego cgvaacpx rnqyr brqhrg vuavhjw fefirxpk yzzzlm enzxhzx jlnyfhxgj dhdtsymr ogixiwup fobhuus wusvbnxxi ephmfpxwj bbbwgi dimwv yemihq bhegr gjdvrfnx qphaxibbk owzdiunk ampgzpw jcfqxi fkxpgurmo qvvjti iczver ciiesh dlgpzkqfj ikhhzz ywxxlv dmhit hpcgxrbd fbwbwhud scsnjgmjr mvjywuy wqswh efvetddl tegewmi kuijtnq denjenyj etjknuw pgygvaq bahoglre zdsux wqwut dxljy uladbbwmr gviyvffq eovqys sfaoq whqrvr netcklxlm mvwxgntg rewlf aekwfwgh ggmziln cqlidbxn fnpssl zctzv qqsnjuguk iilzzaa mt-4">
	                                <label class="form-label" for="jzwjc" id="jzwjc_label">Appointment Category<span class="required">*</span></label>
	                                <span title="" class="k-widget k-dropdown" unselectable="on" role="listbox" aria-haspopup="listbox" aria-expanded="false" tabindex="0" aria-owns="jzwjc_listbox" aria-labelledby="jzwjc_label" aria-live="polite" aria-disabled="false" aria-readonly="false" style="width: 100%;"><span unselectable="on" class="k-dropdown-wrap k-state-default"><span id="sa2143de-d6a5-4e03-ade2-e7482452691a" unselectable="on" role="option" aria-selected="true" class="k-input">--Select--</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span><input id="jzwjc" name="jzwjc" style="width: 100%; display: none;" data-role="dropdownlist"></span>
                                </div></div>
<script>
                                    $(document).ready(function () { 
                                        
$("#gpcmwplx").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaIdData,
                                                    change:gpcmwplx_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#mpnkgnn").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaIdData,
                                                    change:mpnkgnn_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#etfhj").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaIdData,
                                                    change:etfhj_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#xesba").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaIdData,
                                                    change:xesba_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#tznhkij").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaIdData,
                                                    change:tznhkij_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#sswcy").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visasubIdFilterData,
                                                    open: onDrpOpen,
                                                    change:sswcy_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#hcgflcvkb").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visasubIdFilterData,
                                                    open: onDrpOpen,
                                                    change:hcgflcvkb_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#mowfjxdv").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visasubIdFilterData,
                                                    open: onDrpOpen,
                                                    change:mowfjxdv_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#wycprkap").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visasubIdFilterData,
                                                    open: onDrpOpen,
                                                    change:wycprkap_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#pntczgh").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visasubIdFilterData,
                                                    open: onDrpOpen,
                                                    change:pntczgh_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#tkrfk").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: locationFilterData,
                                                    change:tkrfk_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#upiayn").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: locationFilterData,
                                                    change:upiayn_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#jqeixm").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: locationFilterData,
                                                    change:jqeixm_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#dsmdx").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: locationFilterData,
                                                    change:dsmdx_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#phvheo").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: locationFilterData,
                                                    change:phvheo_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#nrtmw").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: missionFilterData,
                                                    change:nrtmw_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#jdqcu").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: missionFilterData,
                                                    change:jdqcu_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#djkksbhsf").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: missionFilterData,
                                                    change:djkksbhsf_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#sgdmhussm").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: missionFilterData,
                                                    change:sgdmhussm_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#mappywgy").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: missionFilterData,
                                                    change:mappywgy_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#adxuvn").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaTypeFilterData,
                                                    change:adxuvn_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#hgshaun").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaTypeFilterData,
                                                    change:hgshaun_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#szjxtz").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaTypeFilterData,
                                                    change:szjxtz_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#qeppq").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaTypeFilterData,
                                                    change:qeppq_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#haibcg").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaTypeFilterData,
                                                    change:haibcg_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#pjirff").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: categoryFilterData,
                                                    open: onDrpOpen,
                                                    change:pjirff_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#cjflcz").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: categoryFilterData,
                                                    open: onDrpOpen,
                                                    change:cjflcz_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#hglorb").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: categoryFilterData,
                                                    open: onDrpOpen,
                                                    change:hglorb_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#ovjkxw").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: categoryFilterData,
                                                    open: onDrpOpen,
                                                    change:ovjkxw_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#jzwjc").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: categoryFilterData,
                                                    open: onDrpOpen,
                                                    change:jzwjc_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
                                         
                                         
                                         
                                         
                                        
                                        
                                    });
                                    
 function gpcmwplx_change(e) {
                                            var dataItem = e.sender.dataItem();

                                             $("#div-jqeixm").hide();
                                            $("#div-nrtmw").hide();
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                            
                                                $("#sswcy").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#hcgflcvkb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#mowfjxdv").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#wycprkap").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pntczgh").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                           
                                          

                                           
                                        }
 function mpnkgnn_change(e) {
                                            var dataItem = e.sender.dataItem();

                                             $("#div-jqeixm").hide();
                                            $("#div-nrtmw").hide();
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                            
                                                $("#sswcy").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#hcgflcvkb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#mowfjxdv").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#wycprkap").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pntczgh").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                           
                                          

                                           
                                        }
 function etfhj_change(e) {
                                            var dataItem = e.sender.dataItem();

                                             $("#div-jqeixm").hide();
                                            $("#div-nrtmw").hide();
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                            
                                                $("#sswcy").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#hcgflcvkb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#mowfjxdv").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#wycprkap").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pntczgh").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                           
                                          

                                           
                                        }
 function xesba_change(e) {
                                            var dataItem = e.sender.dataItem();

                                             $("#div-jqeixm").hide();
                                            $("#div-nrtmw").hide();
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                            
                                                $("#sswcy").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#hcgflcvkb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#mowfjxdv").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#wycprkap").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pntczgh").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                           
                                          

                                           
                                        }
 function tznhkij_change(e) {
                                            var dataItem = e.sender.dataItem();

                                             $("#div-jqeixm").hide();
                                            $("#div-nrtmw").hide();
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                            
                                                $("#sswcy").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#hcgflcvkb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#mowfjxdv").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#wycprkap").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pntczgh").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                           
                                          

                                           
                                        }
 function sswcy_change(e) {
        debugger;
                                            var reSet;
                                            var dataItem = e.sender.dataItem();
                                            $("#DataSource").val(dataItem.Code);

                                            $("#div-jqeixm").hide();
                                            $("#div-nrtmw").hide();
                                            if(dataItem.DepartmentOwnerUserId ==="RLD_VISA" || dataItem.DepartmentOwnerUserId ==="TRT_VISA")
                                            {
                                            missionFilterData = missionData.filter(v => v.Code === "CONSULATE_GUANGZHOU");
                                            }
                                            else
                                            {
                                            missionFilterData = missionData;
                                            }
                                            $("#nrtmw").data("kendoDropDownList").setDataSource(missionFilterData);
                                            if(dataItem.DepartmentOwnerUserId ==="RLD_VISA_VAC" || dataItem.DepartmentOwnerUserId ==="TRT_VISA_VAC" || dataItem.DepartmentOwnerUserId ==="RLD_VISA_NEW" || dataItem.DepartmentOwnerUserId ==="TRP_VISA_NEW" )
                                            {
                                            locationFilterData = locationData.filter(v => v.Code != "GUANGZHOU");
                                            }
                                            else
                                            {
                                            locationFilterData = locationData;
                                            }
                                            $("#jqeixm").data("kendoDropDownList").setDataSource(locationFilterData);
                                            
                                            if (dataItem.Code === 'WEB_EMBASSY')
                                            {
console.log(dataItem.Code);
                                                 $("#div-nrtmw").show();
                                                reSet = $("#jqeixm").data("kendoDropDownList");
                                            }
                                            else
                                            {
console.log(dataItem.Code);
                                                $("#div-jqeixm").show();
                                                reSet = $("#nrtmw").data("kendoDropDownList");
                                            } 
                                                reSet.value('');
                                                $("#cjflcz").data("kendoDropDownList").setDataSource([]);
                                        }
 function hcgflcvkb_change(e) {
        debugger;
                                            var reSet;
                                            var dataItem = e.sender.dataItem();
                                            $("#DataSource").val(dataItem.Code);

                                            $("#div-jqeixm").hide();
                                            $("#div-nrtmw").hide();
                                            if(dataItem.DepartmentOwnerUserId ==="RLD_VISA" || dataItem.DepartmentOwnerUserId ==="TRT_VISA")
                                            {
                                            missionFilterData = missionData.filter(v => v.Code === "CONSULATE_GUANGZHOU");
                                            }
                                            else
                                            {
                                            missionFilterData = missionData;
                                            }
                                            $("#nrtmw").data("kendoDropDownList").setDataSource(missionFilterData);
                                            if(dataItem.DepartmentOwnerUserId ==="RLD_VISA_VAC" || dataItem.DepartmentOwnerUserId ==="TRT_VISA_VAC" || dataItem.DepartmentOwnerUserId ==="RLD_VISA_NEW" || dataItem.DepartmentOwnerUserId ==="TRP_VISA_NEW" )
                                            {
                                            locationFilterData = locationData.filter(v => v.Code != "GUANGZHOU");
                                            }
                                            else
                                            {
                                            locationFilterData = locationData;
                                            }
                                            $("#jqeixm").data("kendoDropDownList").setDataSource(locationFilterData);
                                            
                                            if (dataItem.Code === 'WEB_EMBASSY')
                                            {
console.log(dataItem.Code);
                                                 $("#div-nrtmw").show();
                                                reSet = $("#jqeixm").data("kendoDropDownList");
                                            }
                                            else
                                            {
console.log(dataItem.Code);
                                                $("#div-jqeixm").show();
                                                reSet = $("#nrtmw").data("kendoDropDownList");
                                            } 
                                                reSet.value('');
                                                $("#cjflcz").data("kendoDropDownList").setDataSource([]);
                                        }
 function mowfjxdv_change(e) {
        debugger;
                                            var reSet;
                                            var dataItem = e.sender.dataItem();
                                            $("#DataSource").val(dataItem.Code);

                                            $("#div-jqeixm").hide();
                                            $("#div-nrtmw").hide();
                                            if(dataItem.DepartmentOwnerUserId ==="RLD_VISA" || dataItem.DepartmentOwnerUserId ==="TRT_VISA")
                                            {
                                            missionFilterData = missionData.filter(v => v.Code === "CONSULATE_GUANGZHOU");
                                            }
                                            else
                                            {
                                            missionFilterData = missionData;
                                            }
                                            $("#nrtmw").data("kendoDropDownList").setDataSource(missionFilterData);
                                            if(dataItem.DepartmentOwnerUserId ==="RLD_VISA_VAC" || dataItem.DepartmentOwnerUserId ==="TRT_VISA_VAC" || dataItem.DepartmentOwnerUserId ==="RLD_VISA_NEW" || dataItem.DepartmentOwnerUserId ==="TRP_VISA_NEW" )
                                            {
                                            locationFilterData = locationData.filter(v => v.Code != "GUANGZHOU");
                                            }
                                            else
                                            {
                                            locationFilterData = locationData;
                                            }
                                            $("#jqeixm").data("kendoDropDownList").setDataSource(locationFilterData);
                                            
                                            if (dataItem.Code === 'WEB_EMBASSY')
                                            {
console.log(dataItem.Code);
                                                 $("#div-nrtmw").show();
                                                reSet = $("#jqeixm").data("kendoDropDownList");
                                            }
                                            else
                                            {
console.log(dataItem.Code);
                                                $("#div-jqeixm").show();
                                                reSet = $("#nrtmw").data("kendoDropDownList");
                                            } 
                                                reSet.value('');
                                                $("#cjflcz").data("kendoDropDownList").setDataSource([]);
                                        }
 function wycprkap_change(e) {
        debugger;
                                            var reSet;
                                            var dataItem = e.sender.dataItem();
                                            $("#DataSource").val(dataItem.Code);

                                            $("#div-jqeixm").hide();
                                            $("#div-nrtmw").hide();
                                            if(dataItem.DepartmentOwnerUserId ==="RLD_VISA" || dataItem.DepartmentOwnerUserId ==="TRT_VISA")
                                            {
                                            missionFilterData = missionData.filter(v => v.Code === "CONSULATE_GUANGZHOU");
                                            }
                                            else
                                            {
                                            missionFilterData = missionData;
                                            }
                                            $("#nrtmw").data("kendoDropDownList").setDataSource(missionFilterData);
                                            if(dataItem.DepartmentOwnerUserId ==="RLD_VISA_VAC" || dataItem.DepartmentOwnerUserId ==="TRT_VISA_VAC" || dataItem.DepartmentOwnerUserId ==="RLD_VISA_NEW" || dataItem.DepartmentOwnerUserId ==="TRP_VISA_NEW" )
                                            {
                                            locationFilterData = locationData.filter(v => v.Code != "GUANGZHOU");
                                            }
                                            else
                                            {
                                            locationFilterData = locationData;
                                            }
                                            $("#jqeixm").data("kendoDropDownList").setDataSource(locationFilterData);
                                            
                                            if (dataItem.Code === 'WEB_EMBASSY')
                                            {
console.log(dataItem.Code);
                                                 $("#div-nrtmw").show();
                                                reSet = $("#jqeixm").data("kendoDropDownList");
                                            }
                                            else
                                            {
console.log(dataItem.Code);
                                                $("#div-jqeixm").show();
                                                reSet = $("#nrtmw").data("kendoDropDownList");
                                            } 
                                                reSet.value('');
                                                $("#cjflcz").data("kendoDropDownList").setDataSource([]);
                                        }
 function pntczgh_change(e) {
        debugger;
                                            var reSet;
                                            var dataItem = e.sender.dataItem();
                                            $("#DataSource").val(dataItem.Code);

                                            $("#div-jqeixm").hide();
                                            $("#div-nrtmw").hide();
                                            if(dataItem.DepartmentOwnerUserId ==="RLD_VISA" || dataItem.DepartmentOwnerUserId ==="TRT_VISA")
                                            {
                                            missionFilterData = missionData.filter(v => v.Code === "CONSULATE_GUANGZHOU");
                                            }
                                            else
                                            {
                                            missionFilterData = missionData;
                                            }
                                            $("#nrtmw").data("kendoDropDownList").setDataSource(missionFilterData);
                                            if(dataItem.DepartmentOwnerUserId ==="RLD_VISA_VAC" || dataItem.DepartmentOwnerUserId ==="TRT_VISA_VAC" || dataItem.DepartmentOwnerUserId ==="RLD_VISA_NEW" || dataItem.DepartmentOwnerUserId ==="TRP_VISA_NEW" )
                                            {
                                            locationFilterData = locationData.filter(v => v.Code != "GUANGZHOU");
                                            }
                                            else
                                            {
                                            locationFilterData = locationData;
                                            }
                                            $("#jqeixm").data("kendoDropDownList").setDataSource(locationFilterData);
                                            
                                            if (dataItem.Code === 'WEB_EMBASSY')
                                            {
console.log(dataItem.Code);
                                                 $("#div-nrtmw").show();
                                                reSet = $("#jqeixm").data("kendoDropDownList");
                                            }
                                            else
                                            {
console.log(dataItem.Code);
                                                $("#div-jqeixm").show();
                                                reSet = $("#nrtmw").data("kendoDropDownList");
                                            } 
                                                reSet.value('');
                                                $("#cjflcz").data("kendoDropDownList").setDataSource([]);
                                        }
function tkrfk_change(e) {
                                             debugger;   
                                            dataItem = e.sender.dataItem();
                                           if(dataItem.Id !== 'undefined' || dataItem.Id !== '' || dataItem.Id !== null)
                                           {
                                            //appointmentCatIdFilterData = categoryData.filter(v => v.LegalEntityId === dataItem.Id);
                                            categoryFilterData = categoryData.filter(v => v.LegalEntityId === dataItem.Id);
                                           $("#cjflcz").data("kendoDropDownList").setDataSource(categoryFilterData);
                                            $("#div-cjflcz").show();
                                           }
                                           else
                                           {
                                                $("#cjflcz").data("kendoDropDownList").setDataSource([]);
                                           }
                                        }
function upiayn_change(e) {
                                             debugger;   
                                            dataItem = e.sender.dataItem();
                                           if(dataItem.Id !== 'undefined' || dataItem.Id !== '' || dataItem.Id !== null)
                                           {
                                            //appointmentCatIdFilterData = categoryData.filter(v => v.LegalEntityId === dataItem.Id);
                                            categoryFilterData = categoryData.filter(v => v.LegalEntityId === dataItem.Id);
                                           $("#cjflcz").data("kendoDropDownList").setDataSource(categoryFilterData);
                                            $("#div-cjflcz").show();
                                           }
                                           else
                                           {
                                                $("#cjflcz").data("kendoDropDownList").setDataSource([]);
                                           }
                                        }
function jqeixm_change(e) {
                                             debugger;   
                                            dataItem = e.sender.dataItem();
                                           if(dataItem.Id !== 'undefined' || dataItem.Id !== '' || dataItem.Id !== null)
                                           {
                                            //appointmentCatIdFilterData = categoryData.filter(v => v.LegalEntityId === dataItem.Id);
                                            categoryFilterData = categoryData.filter(v => v.LegalEntityId === dataItem.Id);
                                           $("#cjflcz").data("kendoDropDownList").setDataSource(categoryFilterData);
                                            $("#div-cjflcz").show();
                                           }
                                           else
                                           {
                                                $("#cjflcz").data("kendoDropDownList").setDataSource([]);
                                           }
                                        }
function dsmdx_change(e) {
                                             debugger;   
                                            dataItem = e.sender.dataItem();
                                           if(dataItem.Id !== 'undefined' || dataItem.Id !== '' || dataItem.Id !== null)
                                           {
                                            //appointmentCatIdFilterData = categoryData.filter(v => v.LegalEntityId === dataItem.Id);
                                            categoryFilterData = categoryData.filter(v => v.LegalEntityId === dataItem.Id);
                                           $("#cjflcz").data("kendoDropDownList").setDataSource(categoryFilterData);
                                            $("#div-cjflcz").show();
                                           }
                                           else
                                           {
                                                $("#cjflcz").data("kendoDropDownList").setDataSource([]);
                                           }
                                        }
function phvheo_change(e) {
                                             debugger;   
                                            dataItem = e.sender.dataItem();
                                           if(dataItem.Id !== 'undefined' || dataItem.Id !== '' || dataItem.Id !== null)
                                           {
                                            //appointmentCatIdFilterData = categoryData.filter(v => v.LegalEntityId === dataItem.Id);
                                            categoryFilterData = categoryData.filter(v => v.LegalEntityId === dataItem.Id);
                                           $("#cjflcz").data("kendoDropDownList").setDataSource(categoryFilterData);
                                            $("#div-cjflcz").show();
                                           }
                                           else
                                           {
                                                $("#cjflcz").data("kendoDropDownList").setDataSource([]);
                                           }
                                        }
function nrtmw_change(e) {
                                              debugger;  
                                            dataItem = e.sender.dataItem();
                                            if(dataItem.Id !== 'undefined' || dataItem.Id !== '' || dataItem.Id !== null)
                                           {
                                            appointmentCatIdFilterData = [];
                                            //var filterData = categoryData.filter(v => v.MissionId === dataItem.Id);
                                            appointmentCatIdFilterData = [...new Map(categoryData.map(item =>[item['Code'], item])).values()];
                                        appointmentCatIdFilterData = appointmentCatIdFilterData.filter(item => item.Code === 'CATEGORY_NORMAL');
                                            $("#cjflcz").data("kendoDropDownList").setDataSource(appointmentCatIdFilterData);
                                            $("#div-cjflcz").show();
                                           }
                                            else
                                           {
                                                $("#cjflcz").data("kendoDropDownList").setDataSource([]);
                                           }
                                        }
function jdqcu_change(e) {
                                              debugger;  
                                            dataItem = e.sender.dataItem();
                                            if(dataItem.Id !== 'undefined' || dataItem.Id !== '' || dataItem.Id !== null)
                                           {
                                            appointmentCatIdFilterData = [];
                                            //var filterData = categoryData.filter(v => v.MissionId === dataItem.Id);
                                            appointmentCatIdFilterData = [...new Map(categoryData.map(item =>[item['Code'], item])).values()];
                                        appointmentCatIdFilterData = appointmentCatIdFilterData.filter(item => item.Code === 'CATEGORY_NORMAL');
                                            $("#cjflcz").data("kendoDropDownList").setDataSource(appointmentCatIdFilterData);
                                            $("#div-cjflcz").show();
                                           }
                                            else
                                           {
                                                $("#cjflcz").data("kendoDropDownList").setDataSource([]);
                                           }
                                        }
function djkksbhsf_change(e) {
                                              debugger;  
                                            dataItem = e.sender.dataItem();
                                            if(dataItem.Id !== 'undefined' || dataItem.Id !== '' || dataItem.Id !== null)
                                           {
                                            appointmentCatIdFilterData = [];
                                            //var filterData = categoryData.filter(v => v.MissionId === dataItem.Id);
                                            appointmentCatIdFilterData = [...new Map(categoryData.map(item =>[item['Code'], item])).values()];
                                        appointmentCatIdFilterData = appointmentCatIdFilterData.filter(item => item.Code === 'CATEGORY_NORMAL');
                                            $("#cjflcz").data("kendoDropDownList").setDataSource(appointmentCatIdFilterData);
                                            $("#div-cjflcz").show();
                                           }
                                            else
                                           {
                                                $("#cjflcz").data("kendoDropDownList").setDataSource([]);
                                           }
                                        }
function sgdmhussm_change(e) {
                                              debugger;  
                                            dataItem = e.sender.dataItem();
                                            if(dataItem.Id !== 'undefined' || dataItem.Id !== '' || dataItem.Id !== null)
                                           {
                                            appointmentCatIdFilterData = [];
                                            //var filterData = categoryData.filter(v => v.MissionId === dataItem.Id);
                                            appointmentCatIdFilterData = [...new Map(categoryData.map(item =>[item['Code'], item])).values()];
                                        appointmentCatIdFilterData = appointmentCatIdFilterData.filter(item => item.Code === 'CATEGORY_NORMAL');
                                            $("#cjflcz").data("kendoDropDownList").setDataSource(appointmentCatIdFilterData);
                                            $("#div-cjflcz").show();
                                           }
                                            else
                                           {
                                                $("#cjflcz").data("kendoDropDownList").setDataSource([]);
                                           }
                                        }
function mappywgy_change(e) {
                                              debugger;  
                                            dataItem = e.sender.dataItem();
                                            if(dataItem.Id !== 'undefined' || dataItem.Id !== '' || dataItem.Id !== null)
                                           {
                                            appointmentCatIdFilterData = [];
                                            //var filterData = categoryData.filter(v => v.MissionId === dataItem.Id);
                                            appointmentCatIdFilterData = [...new Map(categoryData.map(item =>[item['Code'], item])).values()];
                                        appointmentCatIdFilterData = appointmentCatIdFilterData.filter(item => item.Code === 'CATEGORY_NORMAL');
                                            $("#cjflcz").data("kendoDropDownList").setDataSource(appointmentCatIdFilterData);
                                            $("#div-cjflcz").show();
                                           }
                                            else
                                           {
                                                $("#cjflcz").data("kendoDropDownList").setDataSource([]);
                                           }
                                        }
 function adxuvn_change(e) {
                                            var dataItem = e.sender.dataItem();
                                            if(dataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                                $("#sswcy").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#hcgflcvkb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#mowfjxdv").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#wycprkap").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pntczgh").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                            }
                                        }
 function hgshaun_change(e) {
                                            var dataItem = e.sender.dataItem();
                                            if(dataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                                $("#sswcy").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#hcgflcvkb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#mowfjxdv").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#wycprkap").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pntczgh").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                            }
                                        }
 function szjxtz_change(e) {
                                            var dataItem = e.sender.dataItem();
                                            if(dataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                                $("#sswcy").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#hcgflcvkb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#mowfjxdv").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#wycprkap").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pntczgh").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                            }
                                        }
 function qeppq_change(e) {
                                            var dataItem = e.sender.dataItem();
                                            if(dataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                                $("#sswcy").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#hcgflcvkb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#mowfjxdv").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#wycprkap").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pntczgh").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                            }
                                        }
 function haibcg_change(e) {
                                            var dataItem = e.sender.dataItem();
                                            if(dataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                                $("#sswcy").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#hcgflcvkb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#mowfjxdv").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#wycprkap").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pntczgh").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                            }
                                        }
 function pjirff_change(e) {
                                           debugger; 
                                            catItem='pjirff';
                                            var dataItem = e.sender.dataItem();
                                            if (dataItem.Code === 'CATEGORY_PREMIUM')
                                            {
                                               $("#PremiumTypeModel").modal('show');
                                            }

                                            function OnPlReject(){
                                                 var apData = $("#cjflcz").data("kendoDropDownList");
                                                    apData.value(-1);
                                                       $("#PremiumTypeModel").modal('hide');
                                                }
                                        }
 function cjflcz_change(e) {
                                           debugger; 
                                            catItem='cjflcz';
                                            var dataItem = e.sender.dataItem();
                                            if (dataItem.Code === 'CATEGORY_PREMIUM')
                                            {
                                               $("#PremiumTypeModel").modal('show');
                                            }

                                            function OnPlReject(){
                                                 var apData = $("#cjflcz").data("kendoDropDownList");
                                                    apData.value(-1);
                                                       $("#PremiumTypeModel").modal('hide');
                                                }
                                        }
 function hglorb_change(e) {
                                           debugger; 
                                            catItem='hglorb';
                                            var dataItem = e.sender.dataItem();
                                            if (dataItem.Code === 'CATEGORY_PREMIUM')
                                            {
                                               $("#PremiumTypeModel").modal('show');
                                            }

                                            function OnPlReject(){
                                                 var apData = $("#cjflcz").data("kendoDropDownList");
                                                    apData.value(-1);
                                                       $("#PremiumTypeModel").modal('hide');
                                                }
                                        }
 function ovjkxw_change(e) {
                                           debugger; 
                                            catItem='ovjkxw';
                                            var dataItem = e.sender.dataItem();
                                            if (dataItem.Code === 'CATEGORY_PREMIUM')
                                            {
                                               $("#PremiumTypeModel").modal('show');
                                            }

                                            function OnPlReject(){
                                                 var apData = $("#cjflcz").data("kendoDropDownList");
                                                    apData.value(-1);
                                                       $("#PremiumTypeModel").modal('hide');
                                                }
                                        }
 function jzwjc_change(e) {
                                           debugger; 
                                            catItem='jzwjc';
                                            var dataItem = e.sender.dataItem();
                                            if (dataItem.Code === 'CATEGORY_PREMIUM')
                                            {
                                               $("#PremiumTypeModel").modal('show');
                                            }

                                            function OnPlReject(){
                                                 var apData = $("#cjflcz").data("kendoDropDownList");
                                                    apData.value(-1);
                                                       $("#PremiumTypeModel").modal('hide');
                                                }
                                        }
                               </script>                    <div class="text-center">
                        <button class="btn btn-primary" id="btnSubmit" type="submit" onclick="return OnSubmitVisaType();">Submit</button>
                    </div>

                </div>
                
                <input id="Data" name="Data" type="hidden" value="wF5igIOP/jrdM5FY/QrQFFQWVnwTogcm4dDCaDVxBiZpNkYqx1aOPQM5LznaYEceIPxg3dt7PPka2UC9BYFcY92IQfy+*********************************+UOSytKye2QzOH5J3iX3RweiKC1qDZwrSJK3NoUx/ukhstqTdEgeQrgIobfmjAEwat5sID7tamfN1MUux1dEM0VmWYSvAgAZDqIxpgLh0p+u92cvCbeSBdGHhUXYT5f3spl1BbTcYcwIlMpQWU7fqCiBTqkMtpHiUBe4X0kFsHZchVHRsRitURwNLBreyLjHjDZ">
                <input id="DataSource" name="DataSource" type="hidden" value="WEB_BLS">
                <input id="ResponseData" name="ResponseData" type="hidden" value="">
                <input id="AppointmentFor" name="AppointmentFor" type="hidden" value="Family">
            <input name="__RequestVerificationToken" type="hidden" value="CfDJ8F1lRAOYCQFFm1KCZ5oQAZUUTGLRIYMp0MWy9Jxuar_P9LmZiBdDOOmJaizoCBt_8lXWhRtRGesjAhOeUWQt_Ul7YmNcsNwUyoN96l67kTFP-S2vUB9AT8lPxr-zCq7RhAGQWzq8DhmOsdxPps-IFd59jlXpJFWmBEEFCzdvN7Cuyk31jMJnidGvzSv38FYctQ"></form>
        </div>
        <div class="d-none d-sm-block col-md-6">
                 <div class="m-0 p-0" style="width: 16rem;"><a href="http://bzx.instar.vip/#/insure-page" target="_blank" rel="noopener noreferrer"><img src="./上海-选类型-一组_files/travel insurance red.jpg" width="100%" class="pull_left"></a></div>
            </div>
</div>
<div class="modal fade" id="familyDisclaimer" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="familyModal" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="familyModal">Family Appointment</h6>
            </div>
            <div class="modal-body scam-body">
                <span style="font-weight:500">
                    Please note that if you are booking family appointment then all members should be part of immediate family.<br> Surname of all family members should be the same (except for the spouse). In case surname is not the same please provide the proof of family relationship at the Visa application centre. <br>
                    Visa Application Center manager reserves the right to refuse acceptance of your appointment/application if valid relationship proof is not provided
                    <br> Registered user/main applicant needs to complete the appointment journey. <br>
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger" type="button" onclick="return OnFamilyReject();">Reject</button>
                <button class="btn btn-success" onclick="return OnFamilyAccept();">Accept</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="commonModal" tabindex="-1" role="dialog" aria-labelledby="commonModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="commonModalLabel">  <span id="commonModalHeader" style="font-weight:600;" class="text-primary"></span></h6>
                <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close">
                </button>

            </div>
            <div class="modal-body scam-body" style="color:black;font-size:medium">
                <span id="commonModalBody">
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" type="button" data-bs-dismiss="modal">Ok</button>
            </div>

        </div>
    </div>
</div>
<div class="modal fade" id="PremiumTypeModel" data-bs-backdrop="static" tabindex="-1" aria-labelledby="PremiumTypeModelLabel" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="payConfirmModalLabel">Premium Category Confirmation</h6>
            </div>
            <div class="modal-body scam-body">
                <span style="font-weight:500">
                  Premium Lounge is an optional service. Please note that the Premium Lounge does not imply obtaining earlier appointments.
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger" type="button" onclick="return OnPlReject();" data-bs-dismiss="modal">Reject</button>
                <button class="btn btn-success" type="button" data-bs-dismiss="modal">Accept</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="existingVisatype" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="familyModal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="familyModal">SchengenVisaPreviousVisa</h6>

            </div>
            <div class="modal-body scam-body">
                <span style="font-weight:500">
                    PreSchengenVisaHolder
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" type="button" data-bs-dismiss="modal">Ok</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="Casa1Visatype" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="familyModal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="familyModal">CASA 1</h6>

            </div>
            <div class="modal-body scam-body">
                <span style="font-weight:500">
                     CASA1
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" type="button" data-bs-dismiss="modal">Ok</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="Casa2Visatype" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="familyModal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="familyModal">CASA 2</h6>

            </div>
            <div class="modal-body scam-body">
                <span style="font-weight:500">
                    CASA2
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" type="button" data-bs-dismiss="modal">Ok</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="Casa3Visatype" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="familyModal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="familyModal">CASA 3</h6>

            </div>
            <div class="modal-body scam-body">
                <span style="font-weight:500">
                     CASA3
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" type="button" data-bs-dismiss="modal">Ok</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" style="z-index:99999;" id="alertModal" tabindex="-1" role="dialog" aria-labelledby="alertModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title">  <span style="font-weight:600;color:black">Know your Jurisdiction</span></h6>
                
                <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close">
                </button>

            </div>
            <div class="modal-body scam-body">
                <table class="table-responsive table-bordered">
  <tbody><tr scope="row">
    <th scope="col">If you are living in
</th>
    <th>You can visit as per your province.
</th>
   
  </tr>
  <tr scope="row">
    <td scope="col">All provinces in the People's Republic of China except the province which belong to the Shanghai, Guangzhou jurisdictions
</td>
    <td scope="col">BLS Spain VAC Beijing, Chongqing, Chengdu, Wuhan, Shenyang, Xi'an and Jinan
</td>

  </tr>
  <tr scope="row">
    <td scope="col">Shanghal, Anhul, Jiangsu, Jiangxi, Nanjing and Zhejiang
</td>
    <td scope="col">BLS Spain VAC Shanghai,Nanjing and Hangzhou
</td>
    
  </tr>
  <tr scope="row">
    <td scope="col">Guangdong, Fujian, Guizhou, Hainan, Hunan, Yunnan and Guangxi(Hukou or residential certificate belong to Guangzhou jurisdiction)
</td>
    <td scope="col">BLS Spain VAC Guangzhou, Shenzhen, Fuzhou, Changsha&nbsp;and&nbsp;Kunming.
</td>
    
  </tr>
  
  
</tbody></table>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" type="button" data-bs-dismiss="modal">OK</button>
            </div>

        </div>
    </div>
</div>
 

    </div>
</div>

    </main>
    <!-- **************** MAIN CONTENT END **************** -->
    <!-- =======================
    Footer START -->
    <footer class="bg-dark pt-5">
        <div class="container">
            <!-- Row START -->
            <div class="row g-4">

                <!-- Widget 1 START -->
                <div class="col-lg-3">
                    <!-- logo -->
                    <a href="https://web.blscn.cn/">
                        <img class="h-40px" src="./上海-选类型-一组_files/logo.png" alt="logo">
                    </a>
                    <h4 class="my-3 text-primary">BLS International</h4>
                </div>
                <!-- Widget 1 END -->
                <!-- Widget 2 START -->
                <div class="col-lg-8 ms-auto">
                    <div class="row g-4">
                        <!-- Link block -->

                        <!-- Link block -->

                        <!-- Link block -->

                        <!-- Link block -->
                    </div>
                </div>
                <!-- Widget 2 END -->

            </div><!-- Row END -->

            <!-- Divider -->
            <hr class="mt-4 mb-0">

            <!-- Bottom footer -->
            <div class="row">
                <div class="container">
                    <div class="d-lg-flex justify-content-between align-items-center py-3 text-center text-lg-start">
                        <!-- copyright text -->
                        <div class="text-muted text-primary-hover">©BLS International 2025.</div>
                        <!-- copyright links-->
                        <div class="nav mt-2 mt-lg-0">
                            <small class="list-inline-item me-0">V&nbsp;4.96</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <!-- =======================
    Footer END -->
    <!-- Back to top -->
    <div class="back-top"></div>
    <!-- Bootstrap JS -->

    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="scamModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="scamModalLabel">  <span style="font-weight:600;color:black">Ready to Leave?</span></h6>
                    <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close">
                    </button>

                </div>
                <div class="modal-body scam-body" style="color:black;font-size:medium">
                    <span>
                        Select "Logout" below if you are ready to end your current session.
                    </span>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-bs-dismiss="modal">Cancel</button>
                    <button class="btn btn-danger" type="button" onclick="OnLogoutSubmit();">Logout</button>
                </div>

            </div>
        </div>
    </div>
    <script src="./上海-选类型-一组_files/bootstrap.bundle.min.js"></script>

    <!-- Vendors -->
    <script src="./上海-选类型-一组_files/tiny-slider.js"></script>
    <script src="./上海-选类型-一组_files/purecounter_vanilla.js"></script>
    <script src="./上海-选类型-一组_files/glightbox.js"></script>
    <script src="./上海-选类型-一组_files/flatpickr.min.js"></script>
    <script src="./上海-选类型-一组_files/choices.min.js"></script>
    <script src="./上海-选类型-一组_files/jarallax.min.js"></script>
    <script src="./上海-选类型-一组_files/jarallax-video.min.js"></script>

    <!-- ThemeFunctions -->
    <script src="./上海-选类型-一组_files/functions.js"></script>
    <script src="./上海-选类型-一组_files/site.js"></script>

    <script>
        var iframeOpenUrl = "";
        var globalPopups = GetStack();
        var globalCallBack = null;
        var globalWindowSender = null;
        function OnLanguageChange(lng) {

            if (lng === 'en-US') {
                return false;
            }
            ShowLoader();
            $.ajax({
                type: "POST",
                url: "/CHN/account/ChangeLanguage?hdnLang=" +lng,
                success: function (response) {
                    HideLoader();
                    if (response != "" && response != null && response.success === true) {
                        window.location.href = window.location.href;
                    }
                },
                error: function (response) {
                    HideLoader();
                    alert("error");
                },
            });
              return false;
        }
        function OnLogout() {
            $('#logoutModal').modal('show');
        }
        function OnLogoutSubmit() {
            ShowLoader();
            $.ajax({
                type: "POST",
                url: "/CHN/account/logout",
                success: function (response) {
                    HideLoader();
                    if (response != "" && response != null && response.success === true) {
                        window.location.href = response.ru;
                    }
                },
                error: function (response) {
                    HideLoader();
                    alert(response.error);
                },
            });
            return false;
        }
    </script>
    
    <script src="./上海-选类型-一组_files/kendo.all.min.js"></script>



<div class="k-list-container k-popup k-group k-reset" id="anadxuvn-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="anadxuvn_listbox" tabindex="0" aria-disabled="false" aria-readonly="false" aria-activedescendant="xc257c74-44a9-4956-8d6e-a638c7f016bb"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel k-state-selected k-state-focused" id="xc257c74-44a9-4956-8d6e-a638c7f016bb">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="anadxuvn_listbox" aria-live="polite" data-role="staticlist" role="listbox"><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="0">2 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="1">3 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="2">4 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="3">5 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="4">6 Members</li></ul></div><div class="k-nodata" style="display:none"><div>No data found.</div></div></div><div class="k-list-container k-popup k-group k-reset" id="anhgshaun-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="anhgshaun_listbox" tabindex="0" aria-disabled="false" aria-readonly="false" aria-activedescendant="w8ea77d2-3469-42f2-a49b-1512b0aeadf5"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel k-state-selected k-state-focused" id="w8ea77d2-3469-42f2-a49b-1512b0aeadf5">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="anhgshaun_listbox" aria-live="polite" data-role="staticlist" role="listbox"><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="0">2 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="1">3 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="2">4 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="3">5 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="4">6 Members</li></ul></div><div class="k-nodata" style="display:none"><div>No data found.</div></div></div><div class="k-list-container k-popup k-group k-reset" id="anszjxtz-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="anszjxtz_listbox" tabindex="0" aria-disabled="false" aria-readonly="false" aria-activedescendant="q3b51a7b-fcb0-4b87-9340-a77f07ad1d81"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel k-state-selected k-state-focused" id="q3b51a7b-fcb0-4b87-9340-a77f07ad1d81">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="anszjxtz_listbox" aria-live="polite" data-role="staticlist" role="listbox"><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="0">2 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="1">3 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="2">4 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="3">5 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="4">6 Members</li></ul></div><div class="k-nodata" style="display:none"><div>No data found.</div></div></div><div class="k-list-container k-popup k-group k-reset" id="anqeppq-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="anqeppq_listbox" tabindex="0" aria-disabled="false" aria-readonly="false" aria-activedescendant="nc40eead-9ef9-4927-9cd3-61d37704d9e9"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel k-state-selected k-state-focused" id="nc40eead-9ef9-4927-9cd3-61d37704d9e9">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="anqeppq_listbox" aria-live="polite" data-role="staticlist" role="listbox"><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="0">2 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="1">3 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="2">4 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="3">5 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="4">6 Members</li></ul></div><div class="k-nodata" style="display:none"><div>No data found.</div></div></div><div class="k-animation-container" style="width: 569.5px; height: 226.859px; box-sizing: content-box; overflow: hidden; display: none; position: absolute; top: 910.047px; z-index: 10002; left: 450px;" aria-hidden="true"><div class="k-list-container k-popup k-group k-reset" id="anhaibcg-list" data-role="popup" aria-hidden="true" style="position: absolute; font-size: 16px; font-family: &quot;DM Sans&quot;, sans-serif; font-stretch: 100%; font-style: normal; font-weight: 400; line-height: normal; width: 563.5px; min-width: 563.5px; white-space: normal; height: auto; display: none; transform: translateY(-226.859px);"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="anhaibcg_listbox" tabindex="0" aria-disabled="false" aria-readonly="false" aria-activedescendant="aceb6a7d-1934-4b9a-83b1-88ccee9b0bfc" style="display: inline-block; width: 563.5px;"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on" style="height: auto;"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="anhaibcg_listbox" aria-live="polite" data-role="staticlist" role="listbox"><li tabindex="-1" role="option" unselectable="on" class="k-item k-state-focused k-state-selected" aria-selected="true" data-offset-index="0" id="aceb6a7d-1934-4b9a-83b1-88ccee9b0bfc">2 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="1">3 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="2">4 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="3">5 Members</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="4">6 Members</li></ul></div><div class="k-nodata" style="display:none"><div>No data found.</div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="gpcmwplx-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="gpcmwplx_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="gpcmwplx_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="mpnkgnn-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="mpnkgnn_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="mpnkgnn_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="etfhj-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="etfhj_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="etfhj_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div><div class="k-animation-container" style="width: 573.25px; height: 134.469px; box-sizing: content-box; overflow: hidden; display: none; position: absolute; top: 469.766px; z-index: 10002; left: 452.5px;" aria-hidden="true"><div class="k-list-container k-popup k-group k-reset" id="xesba-list" data-role="popup" aria-hidden="true" style="position: absolute; font-size: 16px; font-family: &quot;DM Sans&quot;, sans-serif; font-stretch: 100%; font-style: normal; font-weight: 400; line-height: normal; width: 567.25px; min-width: 567.25px; white-space: normal; height: auto; display: none; transform: translateY(-134.469px);"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="xesba_listbox" tabindex="0" aria-disabled="false" aria-readonly="false" aria-activedescendant="m19b7c3c-f36c-4754-982d-0eee76c26dd7" style="display: inline-block; width: 567.25px;"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on" style="height: auto;"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="xesba_listbox" aria-live="polite" data-role="staticlist" role="listbox"><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="0">National Visa</li><li tabindex="-1" role="option" unselectable="on" class="k-item k-state-focused k-state-selected" aria-selected="true" data-offset-index="1" id="m19b7c3c-f36c-4754-982d-0eee76c26dd7">Schengen Visa</li></ul></div><div class="k-nodata" style="display:none"><div>No data found.</div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="tznhkij-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="tznhkij_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="tznhkij_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div><div class="k-animation-container" style="width: 573.25px; height: 257.656px; box-sizing: content-box; overflow: hidden; display: none; position: absolute; top: 591.859px; z-index: 10002; left: 452.5px;" aria-hidden="true"><div class="k-list-container k-popup k-group k-reset" id="sswcy-list" data-role="popup" aria-hidden="true" style="position: absolute; font-size: 16px; font-family: &quot;DM Sans&quot;, sans-serif; font-stretch: 100%; font-style: normal; font-weight: 400; line-height: normal; width: 567.25px; min-width: 567.25px; white-space: normal; height: auto; display: none; transform: translateY(-257.656px);"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="sswcy_listbox" tabindex="0" aria-disabled="false" aria-readonly="false" aria-activedescendant="j8dbff01-95e7-4a43-a1ca-b935b4a85e1f" style="display: inline-block; width: 567.25px;"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on" style="height: auto;"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="sswcy_listbox" aria-live="polite" data-role="staticlist" role="listbox"><li tabindex="-1" role="option" unselectable="on" class="k-item k-state-focused k-state-selected" aria-selected="true" data-offset-index="0" id="j8dbff01-95e7-4a43-a1ca-b935b4a85e1f">Business/Professional Training </li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="1">Tourism</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="2">Visiting family or friends </li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="3">Transit(for seamen)</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="4">Cultural reasons</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="5">Study</li></ul></div><div class="k-nodata" style="display:none"><div>No data found.</div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="hcgflcvkb-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="hcgflcvkb_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="hcgflcvkb_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="mowfjxdv-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="mowfjxdv_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="mowfjxdv_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="wycprkap-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="wycprkap_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="wycprkap_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="pntczgh-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="pntczgh_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="pntczgh_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="tkrfk-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="tkrfk_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="tkrfk_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="upiayn-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="upiayn_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="upiayn_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div><div class="k-animation-container" style="width: 573.25px; height: 206px; box-sizing: content-box; overflow: hidden; display: none; position: absolute; top: 673.953px; z-index: 10002; left: 452.5px;" aria-hidden="true"><div class="k-list-container k-popup k-group k-reset" id="jqeixm-list" data-role="popup" aria-hidden="true" style="position: absolute; font-size: 16px; font-family: &quot;DM Sans&quot;, sans-serif; font-stretch: 100%; font-style: normal; font-weight: 400; line-height: normal; width: 567.25px; min-width: 567.25px; white-space: normal; height: 200px; display: none; transform: translateY(-206px);"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="jqeixm_listbox" tabindex="0" aria-disabled="false" aria-readonly="false" aria-activedescendant="s908ce78-81fa-47a5-ac54-f41957320de0" style="display: inline-block; width: 567.25px;"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on" style="height: 133.125px;"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="jqeixm_listbox" aria-live="polite" data-role="staticlist" role="listbox"><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="0">Wuhan</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="1">Xi'an</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="2">Jinan</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="3">Chongqing</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="4">Shenyang</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="5">Shenzhen</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="6">Changsha</li><li tabindex="-1" role="option" unselectable="on" class="k-item k-state-focused k-state-selected" aria-selected="true" data-offset-index="7" id="s908ce78-81fa-47a5-ac54-f41957320de0">Shanghai</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="8">Fuzhou</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="9">Beijing</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="10">Guangzhou</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="11">Kunming</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="12">Hangzhou</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="13">Nanjing</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="14">Chengdu</li></ul></div><div class="k-nodata" style="display:none"><div>No data found.</div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="dsmdx-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="dsmdx_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="dsmdx_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="phvheo-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="phvheo_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="phvheo_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="nrtmw-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="nrtmw_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="nrtmw_listbox" aria-live="polite" data-role="staticlist" role="listbox"><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="0">Consulate - Beijing</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="1">Consulate-Shanghai</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="2">Consulate-Guangzhou</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="3">Consulate-Chengdu</li></ul></div><div class="k-nodata" style="display:none"><div>No data found.</div></div></div><div class="k-list-container k-popup k-group k-reset" id="jdqcu-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="jdqcu_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="jdqcu_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="djkksbhsf-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="djkksbhsf_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="djkksbhsf_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="sgdmhussm-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="sgdmhussm_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="sgdmhussm_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="mappywgy-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="mappywgy_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="mappywgy_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="pjirff-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="pjirff_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="pjirff_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div><div class="k-animation-container" style="width: 569.5px; height: 134.469px; box-sizing: content-box; overflow: hidden; display: none; position: absolute; top: 1001.73px; z-index: 10002; left: 450px;" aria-hidden="true"><div class="k-list-container k-popup k-group k-reset" id="cjflcz-list" data-role="popup" aria-hidden="true" style="position: absolute; font-size: 16px; font-family: &quot;DM Sans&quot;, sans-serif; font-stretch: 100%; font-style: normal; font-weight: 400; line-height: normal; width: 563.5px; min-width: 563.5px; white-space: normal; height: auto; display: none; transform: translateY(-134.469px);"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="cjflcz_listbox" tabindex="0" aria-disabled="false" aria-readonly="false" style="display: inline-block; width: 563.5px;" aria-activedescendant="m442e38b-4005-4122-ad40-f6f25068fff3"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel k-state-selected k-state-focused" id="m442e38b-4005-4122-ad40-f6f25068fff3">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on" style="height: auto;"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="cjflcz_listbox" aria-live="polite" data-role="staticlist" role="listbox"><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="0">Normal</li><li tabindex="-1" role="option" unselectable="on" class="k-item" aria-selected="false" data-offset-index="1">Premium</li></ul></div><div class="k-nodata" style="display:none"><div>No data found.</div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="hglorb-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="hglorb_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="hglorb_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="ovjkxw-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="ovjkxw_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="ovjkxw_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div><div class="k-list-container k-popup k-group k-reset" id="jzwjc-list" data-role="popup" aria-hidden="true" style="display: none; position: absolute;"><span class="k-list-filter"><input class="k-textbox" role="listbox" aria-haspopup="listbox" aria-expanded="false" aria-owns="jzwjc_listbox" tabindex="0" aria-disabled="false" aria-readonly="false"><span class="k-icon k-i-zoom"></span></span><div class="k-list-optionlabel">--Select--</div><div class="k-group-header" style="display:none"></div><div class="k-list-scroller" unselectable="on"><ul unselectable="on" class="k-list k-reset" tabindex="-1" aria-hidden="true" id="jzwjc_listbox" aria-live="polite" data-role="staticlist" role="listbox"></ul></div><div class="k-nodata" style="display:none"><div></div></div></div></body></html>