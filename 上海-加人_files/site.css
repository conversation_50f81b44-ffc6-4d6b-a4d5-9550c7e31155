﻿.indent-50 {
    text-indent: 50px !important;
}

.indent-30 {
    text-indent: 30px !important;
}

.form-control {
    height: unset !important;
}

.required {
    color: #dc3545;
    padding-left: 5px;
}

.form-control {
    width: 100% !important;
}

input[type=text] {
    border-radius: 5px !important;
    padding: 0.375rem 0.75rem !important;
}
.btn-block {
    width: 100%;
}

.global-overlay {
    height: 100%;
    width: 100%;
    display: none;
    position: fixed;
    z-index: 9999;
    top: 0;
    left: 0;
    background-color: rgb(0,0,0);
    background-color: rgba(0,0,0, 0.3);
}

.global-overlay-loader {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999;
}

    .global-overlay-loader::before,
    .global-overlay-loader::after {
        position: absolute;
        top: 50%;
        left: 50%;
        display: inline-block;
        content: "";
        box-sizing: inherit;
        border-radius: 50%;
        border-style: solid;
        border-color: #606060;
        border-top-color: transparent;
        border-bottom-color: transparent;
        background-color: transparent;
        border-width: 2px;
        font-size: 4em;
    }




    .global-overlay-loader::before {
        margin-top: -.5em;
        margin-left: -.5em;
        width: 1em;
        height: 1em;
        -webkit-animation: loading-animation .7s linear infinite;
        animation: loading-animation .7s linear infinite;
    }


    .global-overlay-loader::after {
        margin-top: -.25em;
        margin-left: -.25em;
        width: .5em;
        height: .5em;
        animation: loading-animation reverse 1.4s linear infinite;
    }

@-webkit-keyframes loading-animation {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes loading-animation {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
